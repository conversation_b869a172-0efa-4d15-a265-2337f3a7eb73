<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Album Artwork</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: none;
        }

        #container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
        }

        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 18px;
            z-index: 1000;
            text-align: center;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            margin-top: 10px;
            font-size: 16px;
        }

        .loading-progress {
            margin-top: 8px;
            font-size: 14px;
            opacity: 0.8;
        }

        .status-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .help-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            font-size: 16px;
            line-height: 1.6;
        }
        
        /* 设置面板样式 */
        .settings-button {
            position: fixed;
            bottom: 15px;
            right: 15px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1500;
            transition: all 0.3s ease;
            opacity: 0.3;
        }
        
        .settings-button:hover {
            opacity: 1;
            background: rgba(30, 30, 30, 0.8);
        }
        
        .settings-icon {
            width: 24px;
            height: 24px;
            fill: #fff;
        }
        
        .settings-panel {
            position: fixed;
            bottom: 65px;
            right: 15px;
            width: 320px;
            background: rgba(20, 20, 20, 0.85);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px;
            color: #fff;
            z-index: 1500;
            display: none;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            max-height: 80vh;
            overflow-y: auto;
        }
        
        /* 设置面板滚动条样式 */
        .settings-panel::-webkit-scrollbar {
            width: 8px;
        }
        
        .settings-panel::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 10px;
        }
        
        .settings-panel::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            transition: background 0.3s ease;
        }
        
        .settings-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .settings-section {
            margin-bottom: 20px;
        }
        
        .settings-section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .settings-item {
            margin-bottom: 12px;
        }
        
        .settings-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .settings-range {
            width: 100%;
            margin-top: 5px;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            background: rgba(60, 60, 60, 0.8);
            border-radius: 3px;
            outline: none;
        }
        
        .settings-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .settings-range::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
        
        .settings-range:focus {
            outline: none;
        }
        
        .settings-select {
            width: 100%;
            background: rgba(60, 60, 60, 0.8);
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
        }
        
        .settings-checkbox {
            margin-right: 8px;
        }
        
        .settings-value {
            display: inline-block;
            width: 40px;
            text-align: right;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .settings-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .settings-color {
            width: 80px;
            height: 25px;
            padding: 0 5px;
            background: rgba(60, 60, 60, 0.8);
            color: #fff;
            border: none;
            border-radius: 4px;
        }
        
        .weight-container {
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        .weight-container label {
            margin-right: 10px;
            font-size: 13px;
            opacity: 0.8;
        }
        
        .weight-slider {
            flex-grow: 1;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            background: rgba(60, 60, 60, 0.8);
            border-radius: 3px;
            outline: none;
        }
        
        .weight-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .weight-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
        
        .settings-description {
            font-size: 12px;
            color: #999;
            margin-left: 10px;
            display: block;
            margin-top: 5px;
        }

        /* iframe播放器样式 */
        .iframe-player {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            width: 70%;
            height: 70vh;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px 20px 0 0;
            z-index: 2000;
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
        }

        .iframe-player.expanded {
            transform: translateX(-50%) translateY(0);
        }

        .iframe-player.collapsed {
            transform: translateX(-50%) translateY(calc(100% - 60px));
        }

        .iframe-topbar {
            height: 60px;
            background: rgba(30, 30, 30, 0.9);
            border-radius: 20px 20px 0 0;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .iframe-album-cover {
            width: 40px;
            height: 40px;
            border-radius: 50%; /* 改为完全圆形 */
            margin-right: 15px;
            background: transparent; /* 背景透明 */
            position: relative;
            overflow: hidden;
        }

        .iframe-album-cover canvas {
            width: 100%;
            height: 100%;
            border-radius: 50%; /* canvas也改为圆形 */
        }

        .iframe-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: 15px;
        }

        .iframe-title {
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .iframe-service {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        #jsi-cherry-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 30px;
            pointer-events: none;
            background-color: transparent;
        }

        /* 樱花飘落动画样式 */
        #jsi-cherry-container.container {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: transparent;
        }

        .iframe-controls {
            display: flex;
            gap: 8px;
        }

        .iframe-control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 8px;
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .iframe-control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .iframe-control-btn.active {
            background: rgba(255, 255, 255, 0.3);
        }

        .iframe-content {
            flex: 1;
            position: relative;
            border-radius: 0 0 20px 20px;
            overflow: hidden;
            min-height: 0; /* 确保flex子项可以收缩 */
            height: 90%;
        }

        .iframe-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #000;
        }

        .iframe-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 16px;
            text-align: center;
        }

        .iframe-loading .loading-spinner {
            width: 32px;
            height: 32px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text" id="loading-text">正在加载专辑封面...</div>
        <div class="loading-progress" id="loading-progress"></div>
    </div>
    
    <!-- 设置按钮 -->
    <div class="settings-button" id="settings-button">
        <svg class="settings-icon" viewBox="0 0 24 24">
            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"></path>
        </svg>
    </div>
    
    <!-- 设置面板 -->
    <div class="settings-panel" id="settings-panel">
        <div class="settings-section">
            <div class="settings-section-title" data-text="layoutSettings">布局设置</div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="rows">行数</label>
                    <span class="settings-value" id="rows-value">6</span>
                </div>
                <input type="range" class="settings-range" id="rows-range" min="2" max="16" value="6">
            </div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="gapSize">间隙大小</label>
                    <span class="settings-value" id="gap-value">0</span>
                </div>
                <input type="range" class="settings-range" id="gap-range" min="0" max="0.1" step="0.01" value="0">
            </div>
        </div>
        
        <div class="settings-section">
            <div class="settings-section-title" data-text="animationSettings">动画设置</div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="animationInterval">动画间隔 (毫秒)</label>
                    <span class="settings-value" id="animation-interval-value">3000</span>
                </div>
                <input type="range" class="settings-range" id="animation-interval-range" min="1000" max="10000" step="500" value="3000">
            </div>
            <div class="settings-item">
                <label class="settings-label" data-text="animationWeights">动画类型权重</label>
                <div class="weight-container">
                    <label data-text="flip">翻转:</label>
                    <input type="range" class="weight-slider" id="flip-weight" min="0" max="100" value="15">
                    <span class="settings-value" id="flip-weight-value">15</span>
                </div>
                <div class="weight-container">
                    <label data-text="drop">掉落:</label>
                    <input type="range" class="weight-slider" id="drop-weight" min="0" max="100" value="15">
                    <span class="settings-value" id="drop-weight-value">15</span>
                </div>
                <div class="weight-container">
                    <label data-text="linkedDrop">连锁掉落:</label>
                    <input type="range" class="weight-slider" id="linked-drop-weight" min="0" max="100" value="15">
                    <span class="settings-value" id="linked-drop-weight-value">15</span>
                </div>
                <div class="weight-container">
                    <label data-text="rollDrop">滚动掉落:</label>
                    <input type="range" class="weight-slider" id="roll-drop-weight" min="0" max="100" value="15">
                    <span class="settings-value" id="roll-drop-weight-value">15</span>
                </div>
                <div class="weight-container">
                    <label data-text="pinRotation">图钉旋转:</label>
                    <input type="range" class="weight-slider" id="pin-rotation-weight" min="0" max="100" value="15">
                    <span class="settings-value" id="pin-rotation-weight-value">15</span>
                </div>
                <div class="weight-container">
                    <label data-text="rowRoll">行滚动:</label>
                    <input type="range" class="weight-slider" id="row-roll-weight" min="0" max="100" value="12.5">
                    <span class="settings-value" id="row-roll-weight-value">12.5</span>
                </div>
                <div class="weight-container">
                    <label data-text="rowDrop">行掉落:</label>
                    <input type="range" class="weight-slider" id="row-drop-weight" min="0" max="100" value="12.5">
                    <span class="settings-value" id="row-drop-weight-value">12.5</span>
                </div>
            </div>
        </div>
        
        <div class="settings-section">
            <div class="settings-section-title" data-text="appearanceSettings">外观设置</div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="backgroundColor">背景颜色</label>
                    <input type="color" class="settings-color" id="background-color" value="#000000">
                </div>
            </div>
        </div>
        
        <div class="settings-section">
            <div class="settings-section-title" data-text="functionSettings">功能设置</div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="desktopMode">桌面模式</label>
                    <input type="checkbox" id="desktop-mode-toggle">
                    <span class="settings-description" data-text="desktopModeDesc">启用后只响应三连击左键，适合作为桌面壁纸</span>
                </div>
            </div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="iframeMode">在当前页面打开音乐网站</label>
                    <input type="checkbox" id="iframe-mode-toggle">
                    <span class="settings-description" data-text="iframeModeDesc">启用后点击音乐链接将在内嵌窗口中打开，而不是新标签页</span>
                </div>
            </div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="iframeMusicBrainzMode">在当前页面打开MusicBrainz</label>
                    <input type="checkbox" id="iframe-musicbrainz-toggle">
                    <span class="settings-description" data-text="iframeMusicBrainzModeDesc">启用后MusicBrainz链接将在内嵌窗口中打开</span>
                </div>
            </div>
            <div class="settings-item">
                <div class="settings-row">
                    <label class="settings-label" data-text="iframeListenBrainzMode">在当前页面打开ListenBrainz</label>
                    <input type="checkbox" id="iframe-listenbrainz-toggle">
                    <span class="settings-description" data-text="iframeListenBrainzModeDesc">启用后ListenBrainz链接将在内嵌窗口中打开</span>
                </div>
            </div>
        </div>
        
        <div id="reset-container" style="text-align: center; margin-top: 20px;"></div>
    </div>

    <!-- iframe播放器 -->
    <div class="iframe-player" id="iframe-player">
        <div class="iframe-topbar">
            <div class="iframe-album-cover" id="iframe-album-cover">
                <canvas id="iframe-showcase-canvas"></canvas>
            </div>
            <div class="iframe-info">
                <div class="iframe-title" id="iframe-title">未选择专辑</div>
                <div class="iframe-service" id="iframe-service">音乐服务</div>
            </div>
            <div id="jsi-cherry-container" class="container"></div>
            <div class="iframe-controls">
                <button class="iframe-control-btn" id="iframe-pin-btn" title="固定/取消固定">📌</button>
                <button class="iframe-control-btn" id="iframe-expand-btn" title="展开/收缩">⬇️</button>
                <button class="iframe-control-btn" id="iframe-close-btn" title="关闭">✖️</button>
            </div>
        </div>
        <div class="iframe-content">
            <div class="iframe-loading" id="iframe-loading">
                <div class="loading-spinner"></div>
                <div>正在加载音乐服务...</div>
            </div>
            <iframe id="iframe-embed" 
                    style="display: none; border-radius: 0 0 20px 20px;" 
                    frameBorder="0" 
                    allowfullscreen="" 
                    allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" 
                    loading="lazy">
            </iframe>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js"></script>

    <!-- jQuery CDN for cherry blossom animation -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- 樱花飘落动画脚本 -->
    <script>
        var RENDERER = {
            INIT_CHERRY_BLOSSOM_COUNT : 30,
            MAX_ADDING_INTERVAL : 10,
            WATCH_INTERVAL : 300,

            init : function(){
                this.isRunning = true;
                this.animationId = null;
                this.setParameters();
                this.reconstructMethods();
                this.setup();
                this.bindEvent();
                this.render();
            },
            setParameters : function(){
                this.$window = $(window);
                this.$container = $('#jsi-cherry-container');
                this.$canvas = $('<canvas />');
                this.context = this.$canvas.appendTo(this.$container).get(0).getContext('2d');
                this.cherries = [];
                this.watchIds = [];
            },
            reconstructMethods : function(){
                this.watchWindowSize = this.watchWindowSize.bind(this);
                this.jdugeToStopResize = this.jdugeToStopResize.bind(this);
                this.render = this.render.bind(this);
            },
            setup : function(){
                this.cherries.length = 0;
                this.watchIds.length = 0;
                this.width = this.$container.width() || 200;
                this.height = this.$container.height() || 30;
                this.$canvas.attr({width : this.width, height : this.height});
                this.maxAddingInterval = Math.round(this.MAX_ADDING_INTERVAL * 1000 / this.width);
                this.addingInterval = this.maxAddingInterval;
                this.createCherries();
            },
            createCherries : function(){
                // 对于小容器，减少樱花数量
                var cherryCount = Math.max(3, Math.round(this.INIT_CHERRY_BLOSSOM_COUNT * this.width / 1000));
                for(var i = 0, length = cherryCount; i < length; i++){
                    this.cherries.push(new CHERRY_BLOSSOM(this, true));
                }
            },
            watchWindowSize : function(){
                this.clearTimer();
                this.tmpWidth = this.$window.width();
                this.tmpHeight = this.$window.height();
                this.watchIds.push(setTimeout(this.jdugeToStopResize, this.WATCH_INTERVAL));
            },
            clearTimer : function(){
                while(this.watchIds.length > 0){
                    clearTimeout(this.watchIds.pop());
                }
            },
            jdugeToStopResize : function(){
                var width = this.$window.width(),
                    height = this.$window.height(),
                    stopped = (width == this.tmpWidth && height == this.tmpHeight);

                this.tmpWidth = width;
                this.tmpHeight = height;

                if(stopped){
                    this.setup();
                }
            },
            bindEvent : function(){
                this.$window.on('resize', this.watchWindowSize);
            },
            render : function(){
                // 检查是否应该停止渲染
                if (!this.isRunning) {
                    return;
                }

                this.animationId = requestAnimationFrame(this.render);

                // 检查context是否还存在
                if (!this.context || !this.context.canvas || !this.context.canvas.parentNode) {
                    this.isRunning = false;
                    return;
                }

                this.context.clearRect(0, 0, this.width, this.height);

                this.cherries.sort(function(cherry1, cherry2){
                    return cherry1.z - cherry2.z;
                });
                for(var i = this.cherries.length - 1; i >= 0; i--){
                    if(!this.cherries[i].render(this.context)){
                        this.cherries.splice(i, 1);
                    }
                }
                if(--this.addingInterval == 0){
                    this.addingInterval = this.maxAddingInterval;
                    this.cherries.push(new CHERRY_BLOSSOM(this, false));
                }
            },

            stop : function(){
                this.isRunning = false;
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
                this.clearTimer();
                this.cherries = [];
            }
        };

        var CHERRY_BLOSSOM = function(renderer, isRandom){
            this.renderer = renderer;
            this.init(isRandom);
        };

        CHERRY_BLOSSOM.prototype = {
            FOCUS_POSITION : 300,
            FAR_LIMIT : 600,
            MAX_RIPPLE_COUNT : 100,
            RIPPLE_RADIUS : 100,
            SURFACE_RATE : 0.5,
            SINK_OFFSET : 20,

            init : function(isRandom){
                this.x = this.getRandomValue(-this.renderer.width, this.renderer.width);
                this.y = isRandom ? this.getRandomValue(0, this.renderer.height) : this.renderer.height * 1.5;
                this.z = this.getRandomValue(0, this.FAR_LIMIT);
                this.vx = this.getRandomValue(-1, 1); // 减小水平速度
                this.vy = -1; // 减小垂直速度
                this.theta = this.getRandomValue(0, Math.PI * 2);
                this.phi = this.getRandomValue(0, Math.PI * 2);
                this.psi = 0;
                this.dpsi = this.getRandomValue(Math.PI / 600, Math.PI / 300);
                this.opacity = 0;
                this.endTheta = false;
                this.endPhi = false;
                this.rippleCount = 0;

                var axis = this.getAxis(),
                    theta = this.theta + Math.ceil(-(this.y + this.renderer.height * this.SURFACE_RATE) / this.vy) * Math.PI / 500;
                theta %= Math.PI * 2;

                // 缩小樱花尺寸以适应小容器
                var scale = Math.min(1, this.renderer.width / 200);
                this.offsetY = 20 * scale * ((theta <= Math.PI / 2 || theta >= Math.PI * 3 / 2) ? -1 : 1);
                this.thresholdY = this.renderer.height / 2 + this.renderer.height * this.SURFACE_RATE * axis.rate;
                this.entityColor = this.renderer.context.createRadialGradient(0, 20 * scale, 0, 0, 20 * scale, 40 * scale);
                this.entityColor.addColorStop(0, 'hsl(330, 70%, ' + 50 * (0.3 + axis.rate) + '%)');
                this.entityColor.addColorStop(0.05, 'hsl(330, 40%,' + 55 * (0.3 + axis.rate) + '%)');
                this.entityColor.addColorStop(1, 'hsl(330, 20%, ' + 70 * (0.3 + axis.rate) + '%)');
                this.shadowColor = this.renderer.context.createRadialGradient(0, 20 * scale, 0, 0, 20 * scale, 40 * scale);
                this.shadowColor.addColorStop(0, 'hsl(330, 40%, ' + 30 * (0.3 + axis.rate) + '%)');
                this.shadowColor.addColorStop(0.05, 'hsl(330, 40%,' + 30 * (0.3 + axis.rate) + '%)');
                this.shadowColor.addColorStop(1, 'hsl(330, 20%, ' + 40 * (0.3 + axis.rate) + '%)');
            },
            getRandomValue : function(min, max){
                return min + (max - min) * Math.random();
            },
            getAxis : function(){
                var rate = this.FOCUS_POSITION / (this.z + this.FOCUS_POSITION),
                    x = this.renderer.width / 2 + this.x * rate,
                    y = this.renderer.height / 2 - this.y * rate;
                return {rate : rate, x : x, y : y};
            },
            renderCherry : function(context, axis){
                // 根据容器大小调整樱花尺寸
                var scale = Math.min(1, this.renderer.width / 200);
                var size = 20 * scale;

                context.beginPath();
                context.moveTo(0, size);
                context.bezierCurveTo(-size * 1.5, size * 0.5, -size * 0.25, -size * 1.5, 0, -size * 0.5);
                context.bezierCurveTo(size * 0.25, -size * 1.5, size * 1.5, size * 0.5, 0, size);
                context.fill();

                for(var i = -2; i < 2; i++){
                    context.beginPath();
                    context.moveTo(0, size);
                    context.quadraticCurveTo(i * 6 * scale, 5 * scale, i * 2 * scale, -12 * scale + Math.abs(i) * scale);
                    context.stroke();
                }
            },
            render : function(context){
                var axis = this.getAxis();

                if(axis.y == this.thresholdY && this.rippleCount < this.MAX_RIPPLE_COUNT){
                    context.save();
                    context.lineWidth = 2;
                    context.strokeStyle = 'hsla(0, 0%, 100%, ' + (this.MAX_RIPPLE_COUNT - this.rippleCount) / this.MAX_RIPPLE_COUNT + ')';
                    context.translate(axis.x + this.offsetY * axis.rate * (this.theta <= Math.PI ? -1 : 1), axis.y);
                    context.scale(1, 0.3);
                    context.beginPath();
                    context.arc(0, 0, this.rippleCount / this.MAX_RIPPLE_COUNT * this.RIPPLE_RADIUS * axis.rate, 0, Math.PI * 2, false);
                    context.stroke();
                    context.restore();
                    this.rippleCount++;
                }
                if(axis.y < this.thresholdY || (!this.endTheta || !this.endPhi)){
                    if(this.y <= 0){
                        this.opacity = Math.min(this.opacity + 0.01, 1);
                    }
                    context.save();
                    context.globalAlpha = this.opacity;
                    context.fillStyle = this.shadowColor;
                    context.strokeStyle = 'hsl(330, 30%,' + 40 * (0.3 + axis.rate) + '%)';
                    context.translate(axis.x, Math.max(axis.y, this.thresholdY + this.thresholdY - axis.y));
                    context.rotate(Math.PI - this.theta);
                    context.scale(axis.rate * -Math.sin(this.phi), axis.rate);
                    context.translate(0, this.offsetY);
                    this.renderCherry(context, axis);
                    context.restore();
                }
                context.save();
                context.fillStyle = this.entityColor;
                context.strokeStyle = 'hsl(330, 40%,' + 70 * (0.3 + axis.rate) + '%)';
                context.translate(axis.x, axis.y + Math.abs(this.SINK_OFFSET * Math.sin(this.psi) * axis.rate));
                context.rotate(this.theta);
                context.scale(axis.rate * Math.sin(this.phi), axis.rate);
                context.translate(0, this.offsetY);
                this.renderCherry(context, axis);
                context.restore();

                if(this.y <= -this.renderer.height / 4){
                    if(!this.endTheta){
                        for(var theta = Math.PI / 2, end = Math.PI * 3 / 2; theta <= end; theta += Math.PI){
                            if(this.theta < theta && this.theta + Math.PI / 200 > theta){
                                this.theta = theta;
                                this.endTheta = true;
                                break;
                            }
                        }
                    }
                    if(!this.endPhi){
                        for(var phi = Math.PI / 8, end = Math.PI * 7 / 8; phi <= end; phi += Math.PI * 3 / 4){
                            if(this.phi < phi && this.phi + Math.PI / 200 > phi){
                                this.phi = Math.PI / 8;
                                this.endPhi = true;
                                break;
                            }
                        }
                    }
                }
                if(!this.endTheta){
                    if(axis.y == this.thresholdY){
                        this.theta += Math.PI / 200 * ((this.theta < Math.PI / 2 || (this.theta >= Math.PI && this.theta < Math.PI * 3 / 2)) ? 1 : -1);
                    }else{
                        this.theta += Math.PI / 500;
                    }
                    this.theta %= Math.PI * 2;
                }
                if(this.endPhi){
                    if(this.rippleCount == this.MAX_RIPPLE_COUNT){
                        this.psi += this.dpsi;
                        this.psi %= Math.PI * 2;
                    }
                }else{
                    this.phi += Math.PI / ((axis.y == this.thresholdY) ? 200 : 500);
                    this.phi %= Math.PI;
                }
                if(this.y <= -this.renderer.height * this.SURFACE_RATE){
                    this.x += 2;
                    this.y = -this.renderer.height * this.SURFACE_RATE;
                }else{
                    this.x += this.vx;
                    this.y += this.vy;
                }
                return this.z > -this.FOCUS_POSITION && this.z < this.FAR_LIMIT && this.x < this.renderer.width * 1.5;
            }
        };
    </script>

    <script>
        class ThreeJSScreensaver {
            constructor() {
                this.container = document.getElementById('container');
                this.loading = document.getElementById('loading');
                this.loadingText = document.getElementById('loading-text');
                this.loadingProgress = document.getElementById('loading-progress');
                this.settingsButton = document.getElementById('settings-button');
                this.settingsPanel = document.getElementById('settings-panel');
                
                // 三连击检测相关变量
                this.clickCount = 0;
                this.clickTimer = null;
                this.clickTimeout = 500; // 连击间隔时间(毫秒)
                this.albums = [];
                this.albumUrls = {};
                this.config = null;
                
                // 设置面板状态
                this.isPanelVisible = false;

                // 多语言支持
                this.language = this.detectLanguage();
                this.texts = this.getTexts();

                // MusicBrainz 相关
                this.musicBrainzCollectionIds = this.parseCollectionIdsFromUrl();
                this.coverCache = new Map(); // 封面缓存
                this.albumsCache = []; // 专辑列表缓存
                this.albumsTimestamp = 0; // 专辑列表缓存时间戳
                this.externalLinksCache = new Map(); // 外部链接缓存
                this.lastApiCallTime = 0; // 用于API调用节流
                // this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时过期 - 已移除过期时间，缓存永久有效

                // Three.js 相关
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.covers = [];
                this.animationMixer = null;
                this.clock = new THREE.Clock();

                // 网格参数
                this.gridRows = 6; // 行数，可由用户自定义
                this.coverSize = 1; // 基础封面大小，会根据行数自动计算
                this.gap = 0; // 间隙大小

                // 封面分配相关
                this.usedAlbums = []; // 已使用的专辑封面索引
                this.isInitializing = true; // 是否正在初始化

                // 动画相关
                this.animationInterval = null;
                this.activeAnimations = [];
                this.animationIntervalTime = 3000; // 默认动画间隔时间
                this.animationWeights = {
                    flip: 15,
                    drop: 15,
                    linkedDrop: 15,
                    rollDrop: 15,
                    pinRotation: 15,
                    rowRollDrop: 12.5,
                    rowDrop: 12.5
                };
                
                // 3D展示台相关属性
                this.showcaseScene = null;
                this.showcaseCamera = null;
                this.showcaseRenderer = null;
                this.showcaseCover = null;
                this.showcaseStand = null;
                this.showcaseLights = [];
                this.showcaseAnimationId = null;
                this.mousePosition = { x: 0, y: 0 };
                this.showcaseActive = false;
                
                // 全屏鼠标追踪相关属性
                this.showcaseMouseMoveHandler = null;
                this.showcaseMouseIdleHandler = null;
                this.mouseIdleTimer = null;
                
                // iframe播放器相关属性
                this.iframeModeEnabled = false;
                this.iframeMusicBrainzModeEnabled = false;
                this.iframeListenBrainzModeEnabled = false;
                this.iframePlayer = document.getElementById('iframe-player');
                this.iframeEmbed = document.getElementById('iframe-embed');
                this.iframeLoading = document.getElementById('iframe-loading');
                this.iframeTitle = document.getElementById('iframe-title');
                this.iframeService = document.getElementById('iframe-service');
                this.iframeAlbumCover = document.getElementById('iframe-album-cover');
                this.iframePinBtn = document.getElementById('iframe-pin-btn');
                this.iframeExpandBtn = document.getElementById('iframe-expand-btn');
                this.iframeCloseBtn = document.getElementById('iframe-close-btn');
                this.iframeShowcaseCanvas = document.getElementById('iframe-showcase-canvas');
                
                // iframe状态
                this.isIframePinned = false;
                this.isIframeExpanded = false;
                this.iframeMouseLeaveTimer = null;
                this.currentIframeUrl = null;
                this.currentAlbumData = null;
                
                // iframe 3D展示台
                this.iframeShowcaseScene = null;
                this.iframeShowcaseCamera = null;
                this.iframeShowcaseRenderer = null;
                this.iframeShowcaseCover = null;
                this.iframeShowcaseAnimationId = null;

                // iframe事件处理器
                this.iframeMouseEnterHandler = null;
                this.iframeMouseLeaveHandler = null;
                

                
                // 从本地存储加载设置
                this.loadSettings();

                // 更新UI文本为对应语言
                this.updateUITexts();

                this.init();
            }

            // 更新UI中的文本为对应语言
            updateUITexts() {
                // 更新所有带有 data-text 属性的元素
                const elementsWithText = document.querySelectorAll('[data-text]');
                elementsWithText.forEach(element => {
                    const textKey = element.getAttribute('data-text');
                    if (this.texts[textKey]) {
                        // 检查是否是 label 元素且包含冒号，如果是则保留冒号
                        if (element.tagName.toLowerCase() === 'label' && element.textContent.includes(':')) {
                            element.textContent = this.texts[textKey] + ':';
                        } else {
                            element.textContent = this.texts[textKey];
                        }
                    }
                });
            }

            // 检测用户语言
            detectLanguage() {
                // 检查浏览器语言设置
                const browserLang = navigator.language || navigator.userLanguage;

                // 如果是中文相关的语言代码，使用中文
                if (browserLang.startsWith('zh')) {
                    return 'zh';
                }

                // 默认使用英文
                return 'en';
            }

            // 获取多语言文本
            getTexts() {
                const texts = {
                    zh: {
                        loading: '正在加载专辑封面...',
                        loadingFromCollection: '正在从 {count} 个 MusicBrainz 收藏栏获取专辑...',
                        loadingCollection: '正在获取收藏栏 {current}/{total}...',
                        loadingCovers: '正在加载专辑封面...',
                        loadingProgress: '({current}/{total})',
                        loadComplete: '加载完成！',
                        errorNetwork: '加载专辑封面失败，请检查网络连接',
                        errorCollection: '无法从指定的 MusicBrainz 收藏栏获取专辑，请检查收藏栏 ID 是否正确：{ids}',
                        helpEsc: '按ESC键退出屏保',
                        loadingBackup: '正在加载本地专辑封面...',
                        usingLocalMode: '使用本地模式',
                        noCoversFound: '没有找到可用的专辑封面，请确保 cover 目录中有图片文件',
                        openedLinks: '已打开: {links}',
                        noExternalLinks: '已打开MusicBrainz (无法获取其他链接)',
                        externalLinksFor: '专辑外部链接',
                        noLinksFound: '没有找到外部链接',
                        loadingLinks: '正在加载外部链接...',
                        externalLinksReady: '外部链接已就绪',
                        linkCopied: '{service}链接已复制到剪贴板',
                        scanningDirectory: '正在扫描 cover 目录...',
                        // 设置面板文本
                        layoutSettings: '布局设置',
                        rows: '行数',
                        gapSize: '间隙大小',
                        animationSettings: '动画设置',
                        animationInterval: '动画间隔 (毫秒)',
                        animationWeights: '动画类型权重',
                        flip: '翻转',
                        drop: '掉落',
                        linkedDrop: '连锁掉落',
                        rollDrop: '滚动掉落',
                        pinRotation: '图钉旋转',
                        rowRoll: '行滚动',
                        rowDrop: '行掉落',
                        appearanceSettings: '外观设置',
                        backgroundColor: '背景颜色',
                        functionSettings: '功能设置',
                        desktopMode: '桌面模式',
                        desktopModeDesc: '启用后只响应三连击左键，适合作为桌面壁纸',
                        resetAllSettings: '重置所有设置',
                        clearCache: '清除缓存',
                        settingsSaved: '设置已保存',
                        settingsSaveFailed: '设置保存失败',
                        desktopModeEnabled: '已启用桌面模式，只响应三连击左键',
                        desktopModeDisabled: '已禁用桌面模式，正常响应点击事件',
                        allSettingsReset: '所有设置已重置为默认值',
                        allCacheCleared: '所有缓存已清除，页面将在3秒后刷新'
                    },
                    en: {
                        loading: 'Loading album covers...',
                        loadingFromCollection: 'Loading albums from {count} MusicBrainz collections...',
                        loadingCollection: 'Loading collection {current}/{total}...',
                        loadingCovers: 'Loading album covers...',
                        loadingProgress: '({current}/{total})',
                        loadComplete: 'Loading complete!',
                        errorNetwork: 'Failed to load album covers, please check your network connection',
                        errorCollection: 'Unable to load albums from specified MusicBrainz collections, please check collection IDs: {ids}',
                        helpEsc: 'Press ESC to exit screensaver',
                        loadingBackup: 'Loading local album covers...',
                        usingLocalMode: 'Using local mode',
                        noCoversFound: 'No album covers found, please make sure there are image files in the cover directory',
                        openedLinks: 'Opened: {links}',
                        noExternalLinks: 'Opened MusicBrainz (could not get other links)',
                        externalLinksFor: 'External Links for Album',
                        noLinksFound: 'No external links found',
                        loadingLinks: 'Loading external links...',
                        externalLinksReady: 'External links ready',
                        linkCopied: '{service} link copied to clipboard',
                        scanningDirectory: 'Scanning cover directory...',
                        // 设置面板文本
                        layoutSettings: 'Layout Settings',
                        rows: 'Rows',
                        gapSize: 'Gap Size',
                        animationSettings: 'Animation Settings',
                        animationInterval: 'Animation Interval (ms)',
                        animationWeights: 'Animation Type Weights',
                        flip: 'Flip',
                        drop: 'Drop',
                        linkedDrop: 'Linked Drop',
                        rollDrop: 'Roll Drop',
                        pinRotation: 'Pin Rotation',
                        rowRoll: 'Row Roll',
                        rowDrop: 'Row Drop',
                        appearanceSettings: 'Appearance Settings',
                        backgroundColor: 'Background Color',
                        functionSettings: 'Function Settings',
                        desktopMode: 'Desktop Mode',
                        desktopModeDesc: 'When enabled, only responds to triple-click, suitable for desktop wallpaper',
                        resetAllSettings: 'Reset All Settings',
                        clearCache: 'Clear Cache',
                        settingsSaved: 'Settings saved',
                        settingsSaveFailed: 'Failed to save settings',
                        desktopModeEnabled: 'Desktop mode enabled, only responds to triple-click',
                        desktopModeDisabled: 'Desktop mode disabled, responds to normal clicks',
                        allSettingsReset: 'All settings have been reset to default values',
                        allCacheCleared: 'All cache cleared, page will refresh in 3 seconds'
                    }
                };

                return texts[this.language];
            }

            // 格式化文本（替换占位符）
            formatText(key, params = {}) {
                let text = this.texts[key];
                for (const [param, value] of Object.entries(params)) {
                    text = text.replace(`{${param}}`, value);
                }
                return text;
            }

            // 从URL参数中解析收藏夹ID
            parseCollectionIdsFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);

                // 检查是否指定了local参数
                if (urlParams.has('local')) {
                    console.log('使用本地模式');
                    return [];
                }

                const ids = urlParams.getAll('id'); // 获取所有id参数

                if (ids.length > 0) {
                    console.log(`从URL参数获取到 ${ids.length} 个收藏夹ID:`, ids);
                    return ids;
                } else {
                    // 如果没有URL参数，使用默认收藏夹ID
                    const defaultId = '8ac56a60-c667-4603-817e-793f0d2600b8';
                    console.log('使用默认收藏夹ID:', defaultId);
                    return [defaultId];
                }
            }

            async init() {
                try {
                    // 初始化桌面模式变量（默认关闭）
                    if (this.desktopMode === undefined) {
                        this.desktopMode = false;
                    }
                    
                    // 初始化加载消息
                    this.updateLoadingMessage(this.texts.loading);

                    // await this.loadConfig();
                    await this.loadCoverCache();
                    
                    // 检查是否是本地模式
                    const isLocalMode = window.location.search.includes('local=true');
                    
                    if (isLocalMode) {
                        // 本地模式：直接扫描本地目录
                        console.log('使用本地模式，扫描本地图片目录');
                        this.updateLoadingMessage('使用本地模式，扫描本地图片...');
                        await this.loadAlbumsFromBackup();
                    } else if (this.albumsCache.length > 0) {
                        // 有缓存：使用缓存
                        console.log('使用缓存的专辑列表');
                        this.updateLoadingMessage(this.texts.loadingCovers);
                        
                        // 使用缓存的专辑列表
                        await this.loadAlbumsFromCache();
                        
                        // 在后台获取最新的专辑列表并比对
                        this.checkForAlbumsUpdate();
                    } else {
                        // 无缓存且非本地模式：获取MusicBrainz数据
                        console.log('没有缓存的专辑列表，从 MusicBrainz 获取');
                        // 最多尝试3次
                        let retryCount = 0;
                        const maxRetries = 3;
                        let success = false;
                        
                        while (retryCount < maxRetries && !success) {
                            try {
                                await this.loadAlbumsFromMusicBrainz();
                                success = true;
                            } catch (error) {
                                retryCount++;
                                console.warn(`从 MusicBrainz 获取专辑失败 (尝试 ${retryCount}/${maxRetries})`, error);
                                if (retryCount < maxRetries) {
                                    // 等待一段时间后重试
                                    await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                                    console.log(`重试获取 MusicBrainz 数据 (${retryCount}/${maxRetries})...`);
                                }
                            }
                        }
                    }

                    // 如果专辑列表仍为空，尝试使用本地备份
                    if (this.albums.length === 0) {
                        console.warn('专辑列表为空，尝试使用本地备份');
                        await this.loadAlbumsFromBackup();
                    }

                    this.updateLoadingMessage(this.texts.loadComplete);

                    this.initThreeJS();
                    this.createGrid();
                    this.isInitializing = false; // 初始化完成
                    this.hideLoading();
                    this.startAnimations();
                    this.bindEvents();
                    this.initIframePlayer(); // 初始化iframe播放器
                    this.animate();
                } catch (error) {
                    console.error('初始化失败:', error);

                    // 只有在用户明确要求使用本地模式时才使用本地模式作为备选方案
                    if (window.location.search.includes('local=true')) {
                        try {
                            console.warn('用户指定使用本地模式，尝试作为备选方案');
                            await this.loadAlbumsFromBackup();
                            
                            this.updateLoadingMessage(this.texts.loadComplete);
                            this.initThreeJS();
                            this.createGrid();
                            this.isInitializing = false;
                            this.hideLoading();
                            this.startAnimations();
                            this.bindEvents();
                            this.initIframePlayer(); // 初始化iframe播放器
                            this.animate();
                        } catch (backupError) {
                            console.error('本地模式也失败:', backupError);
                            this.showError(this.texts.errorNetwork);
                        }
                    } else {
                        // 如果指定了 MusicBrainz ID 但获取失败，显示具体错误信息
                        if (this.musicBrainzCollectionIds.length > 0) {
                            this.showError(this.formatText('errorCollection', {
                                ids: this.musicBrainzCollectionIds.join(', ')
                            }));
                        } else {
                            this.showError(this.texts.errorNetwork);
                        }
                    }
                }
            }

            async loadConfig() {
                try {
                    const response = await fetch('./config.json?t=' + Date.now());
                    if (response.ok) {
                        this.config = await response.json();
                        console.log('✅ 配置加载成功');
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('❌ 配置加载失败:', error.message);
                    console.warn('使用默认配置');
                    this.config = {
                        screensaver: {
                            layout: {
                                coverSize: { min: 120, max: 300, default: 200 },
                                columns: { min: 3, max: 12, preferred: 12 },
                                layoutMode: "horizontal"
                            },
                            animation: {
                                enabled: true,
                                interval: { min: 3000, max: 8000 },
                                types: {
                                    flip: { enabled: true, weight: 40 },
                                    drop: { enabled: true, weight: 35 },
                                    linkedDrop: { enabled: true, weight: 25 }
                                }
                            }
                        }
                    };
                }
            }

            // 加载缓存
            async loadCoverCache() {
                try {
                    // 加载封面缓存
                    const cached = localStorage.getItem('musicbrainz_cover_cache');
                    if (cached) {
                        const cacheData = JSON.parse(cached);
                        // 不再检查缓存是否过期，直接加载所有缓存
                        for (const [key, value] of Object.entries(cacheData)) {
                            this.coverCache.set(key, value);
                        }
                        console.log(`✅ 加载了 ${this.coverCache.size} 个缓存的封面`);
                    }
                    
                    // 加载专辑列表缓存
                    const cachedAlbums = localStorage.getItem('musicbrainz_albums_cache');
                    if (cachedAlbums) {
                        const albumsData = JSON.parse(cachedAlbums);
                        this.albumsCache = albumsData.albums || [];
                        this.albumsTimestamp = albumsData.timestamp || 0;
                        console.log(`✅ 加载了缓存的专辑列表，共 ${this.albumsCache.length} 个专辑`);
                    }
                } catch (error) {
                    console.warn('加载缓存失败:', error);
                }
            }

            // 保存缓存
            saveCoverCache() {
                try {
                    // 保存封面缓存
                    const cacheData = {};
                    for (const [key, value] of this.coverCache.entries()) {
                        cacheData[key] = value;
                    }
                    localStorage.setItem('musicbrainz_cover_cache', JSON.stringify(cacheData));
                    console.log('✅ 封面缓存已保存');
                } catch (error) {
                    console.warn('保存封面缓存失败:', error);
                }
            }
            
            // 保存专辑列表缓存
            saveAlbumsCache(albums) {
                try {
                    const albumsData = {
                        albums: albums,
                        timestamp: Date.now()
                    };
                    localStorage.setItem('musicbrainz_albums_cache', JSON.stringify(albumsData));
                    this.albumsCache = albums;
                    this.albumsTimestamp = albumsData.timestamp;
                    console.log(`✅ 专辑列表缓存已保存，共 ${albums.length} 个专辑`);
                } catch (error) {
                    console.warn('保存专辑列表缓存失败:', error);
                }
            }

            // 从缓存加载专辑列表
            async loadAlbumsFromCache() {
                try {
                    console.log('从缓存加载专辑列表...');
                    
                    // 清空当前专辑列表
                    this.albums = [];
                    this.albumUrls = {};
                    
                    // 加载每个专辑的封面
                    let loadedCount = 0;
                    const totalCount = this.albumsCache.length;
                    
                    // 并发加载封面，每批次20个
                    const batchSize = 20;
                    for (let i = 0; i < totalCount; i += batchSize) {
                        const batch = this.albumsCache.slice(i, i + batchSize);
                        const promises = batch.map(async (release) => {
                            try {
                                const coverUrl = await this.getCoverArt(release.id);
                                // 直接添加到列表，不检查是否为空
                                this.albums.push(coverUrl);
                                this.albumUrls[coverUrl] = `https://musicbrainz.org/release/${release.id}`;
                                loadedCount++;
                                return 1;
                            } catch (error) {
                                console.warn(`无法加载缓存的专辑封面: ${release.title || release.id}`, error);
                                return 0;
                            }
                        });
                        
                        // 等待当前批次完成
                        await Promise.all(promises);
                        
                        // 更新加载进度
                        this.updateLoadingMessage(
                            this.texts.loadingCovers,
                            this.formatText('loadingProgress', {
                                current: loadedCount,
                                total: totalCount
                            })
                        );
                    }
                    
                    // 不再检查专辑数量是否为零，因为我们现在总是添加封面到列表中
                    
                    console.log(`✅ 从缓存成功加载 ${this.albums.length} 张专辑封面`);
                    
                } catch (error) {
                    console.error('从缓存加载专辑失败:', error);
                    // 如果从缓存加载失败，尝试从 MusicBrainz 获取
                    await this.loadAlbumsFromMusicBrainz();
                }
            }
            
            // 在后台检查专辑列表更新
            async checkForAlbumsUpdate() {
                setTimeout(async () => {
                    try {
                        console.log('后台检查专辑列表更新...');
                        
                        // 获取最新的专辑列表
                        const allReleases = await this.fetchAllReleases();
                        
                        // 比较新旧列表是否有变化
                        const oldIds = new Set(this.albumsCache.map(release => release.id));
                        const newIds = new Set(allReleases.map(release => release.id));
                        
                        // 检查是否有新增或删除的专辑
                        let hasChanges = false;
                        
                        // 检查新增
                        for (const id of newIds) {
                            if (!oldIds.has(id)) {
                                hasChanges = true;
                                break;
                            }
                        }
                        
                        // 检查删除
                        if (!hasChanges) {
                            for (const id of oldIds) {
                                if (!newIds.has(id)) {
                                    hasChanges = true;
                                    break;
                                }
                            }
                        }
                        
                        if (hasChanges) {
                            console.log('专辑列表有更新，保存新列表并刷新页面');
                            // 保存新的专辑列表
                            this.saveAlbumsCache(allReleases);
                            // 延迟一秒后刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            console.log('专辑列表没有变化');
                        }
                        
                    } catch (error) {
                        console.warn('后台检查专辑列表更新失败:', error);
                        // 失败也没关系，不影响当前体验
                    }
                }, 5000); // 延迟5秒后在后台检查，避免影响初始加载体验
            }
            
            // 获取所有专辑列表
            async fetchAllReleases() {
                const allReleases = [];
                const releaseIds = new Set(); // 用于去重
                
                for (let i = 0; i < this.musicBrainzCollectionIds.length; i++) {
                    const collectionId = this.musicBrainzCollectionIds[i];
                    
                    try {
                        const releases = await this.fetchCollectionReleases(collectionId);
                        console.log(`从收藏栏 ${collectionId} 获取到 ${releases.length} 个专辑`);
                        
                        // 去重添加专辑
                        for (const release of releases) {
                            if (!releaseIds.has(release.id)) {
                                releaseIds.add(release.id);
                                allReleases.push(release);
                            }
                        }
                    } catch (error) {
                        console.warn(`获取收藏栏 ${collectionId} 失败:`, error);
                        // 如果获取失败，继续下一个收藏栏
                    }
                }
                
                console.log(`合并后共获取到 ${allReleases.length} 个不重复专辑`);
                return allReleases;
            }
            
            // 从 MusicBrainz 收藏栏加载专辑
            async loadAlbumsFromMusicBrainz() {
                try {
                    this.updateLoadingMessage(this.formatText('loadingFromCollection', {
                        count: this.musicBrainzCollectionIds.length
                    }));

                    // 从所有收藏栏获取专辑列表
                    const allReleases = await this.fetchAllReleases();
                    
                    // 保存专辑列表到缓存
                    this.saveAlbumsCache(allReleases);
                    
                    console.log(`合并后共获取到 ${allReleases.length} 个不重复专辑`);
                    this.updateLoadingMessage(this.texts.loadingCovers);

                    // 获取每个专辑的封面 - 使用并发请求
                    let loadedCount = 0;
                    const totalCount = allReleases.length;
                    
                    // 并发加载封面，每批次15个
                    const batchSize = 15;
                    for (let i = 0; i < totalCount; i += batchSize) {
                        const batch = allReleases.slice(i, i + batchSize);
                        const promises = batch.map(async (release) => {
                            // 对每个封面添加重试机制
                            let retryCount = 0;
                            const maxRetries = 2;
                            
                            while (retryCount <= maxRetries) {
                                try {
                                    const coverUrl = await this.getCoverArt(release.id);
                                    // 直接添加到列表，不检查是否为空
                                    this.albums.push(coverUrl);
                                    this.albumUrls[coverUrl] = `https://musicbrainz.org/release/${release.id}`;
                                    loadedCount++;
                                    return 1;
                                } catch (error) {
                                    retryCount++;
                                    if (retryCount <= maxRetries) {
                                        console.warn(`获取封面失败，重试 (${retryCount}/${maxRetries}): ${release.title || release.id}`);
                                        // 等待一段时间后重试
                                        await new Promise(resolve => setTimeout(resolve, 300 * retryCount));
                                    } else {
                                        console.warn(`无法加载专辑封面: ${release.title || release.id}`, error);
                                        return 0;
                                    }
                                }
                            }
                            return 0;
                        });
                        
                        // 等待当前批次完成
                        await Promise.all(promises);
                        
                        // 更新加载进度
                        this.updateLoadingMessage(
                            this.texts.loadingCovers,
                            this.formatText('loadingProgress', {
                                current: loadedCount,
                                total: totalCount
                            })
                        );
                    }

                    console.log(`✅ 成功加载 ${this.albums.length} 张专辑封面`);
                    this.saveCoverCache(); // 保存缓存

                } catch (error) {
                    console.error('❌ 从 MusicBrainz 加载专辑失败:', error.message);
                    // 不自动回退到本地模式，而是抛出错误
                    throw new Error(this.formatText('errorCollection', {
                        ids: this.musicBrainzCollectionIds.join(', ')
                    }) + `: ${error.message}`);
                }
            }

            // 获取 MusicBrainz 收藏栏中的专辑列表
            async fetchCollectionReleases(collectionId) {
                // 使用 CORS 代理来访问 MusicBrainz API
                const proxyUrls = [
                    'https://corsproxy.io/?',
                    'https://api.allorigins.win/raw?url=',
                    'https://cors-anywhere.herokuapp.com/'
                ];
                
                const targetUrl = `https://musicbrainz.org/ws/2/release?collection=${collectionId}&limit=100&fmt=json`;
                
                // 尝试不同的代理
                for (let i = 0; i < proxyUrls.length; i++) {
                    const proxyUrl = proxyUrls[i];
                    const url = proxyUrl + encodeURIComponent(targetUrl);
                    
                    try {
                        console.log(`尝试使用代理 ${i+1}/${proxyUrls.length} 获取收藏栏 ${collectionId}`);
                        
                        // 添加重试机制
                        let retryCount = 0;
                        const maxRetries = 2;
                        
                        while (retryCount <= maxRetries) {
                            try {
                                const response = await fetch(url);
                                
                                if (!response.ok) {
                                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                }
                                
                                // 解析响应
                                const data = await response.json();
                                console.log(`✅ 成功获取收藏栏 ${collectionId} (使用代理 ${i+1})`);
                                return data.releases || [];
                            } catch (fetchError) {
                                retryCount++;
                                if (retryCount <= maxRetries) {
                                    console.warn(`获取收藏栏失败，重试 (${retryCount}/${maxRetries})`, fetchError);
                                    // 等待一段时间后重试
                                    await new Promise(resolve => setTimeout(resolve, 500 * retryCount));
                                } else {
                                    // 超过重试次数，尝试下一个代理
                                    console.error(`使用代理 ${i+1} 获取收藏栏 ${collectionId} 失败:`, fetchError);
                                    break;
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`使用代理 ${i+1} 获取收藏栏 ${collectionId} 失败:`, error);
                        // 继续尝试下一个代理
                    }
                }
                
                // 所有代理都失败了
                throw new Error(`无法获取收藏栏 ${collectionId}，所有代理都失败`);
            }

            // 获取专辑封面
            async getCoverArt(releaseId) {
                // 先检查缓存
                const cacheKey = `cover_${releaseId}`;
                if (this.coverCache.has(cacheKey)) {
                    const cached = this.coverCache.get(cacheKey);
                    console.log(`使用缓存的封面: ${releaseId}`);
                    return cached.url;
                }

                // 从 Cover Art Archive 获取封面 URL (不进行检测)
                const url = `https://coverartarchive.org/release/${releaseId}/front-500`;
                
                // 直接将 URL 保存到缓存中，不检测是否可用
                console.log(`添加封面到缓存: ${releaseId}`);
                this.coverCache.set(cacheKey, {
                    url: url,
                    timestamp: Date.now()
                });
                
                return url;
            }

            async loadAlbumsFromBackup() {
                console.log('使用本地专辑列表...');
                this.updateLoadingMessage(this.texts.loadingBackup);

                try {
                    // 清空现有专辑列表，确保使用本地图片
                    this.albums = [];
                    this.albumUrls = {};
                    
                    // 获取当前页面的基础URL
                    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
                    const coverDirUrl = `${baseUrl}cover/`;
                    
                    // 自动扫描 cover 目录
                    console.log('自动扫描 cover 目录...');
                    this.updateLoadingMessage(this.texts.scanningDirectory);
                    
                    try {
                        // 扫描目录 - 新的scanCoverDirectory函数会直接添加所有已知图片
                        await this.scanCoverDirectory(coverDirUrl);
                    } catch (scanError) {
                        console.warn('扫描目录失败:', scanError);
                        
                        // 如果扫描完全失败，添加一个默认图片
                        if (this.albums.length === 0) {
                            const defaultImage = `${coverDirUrl}1.jpg`;
                            this.albums.push(defaultImage);
                            this.albumUrls[defaultImage] = this.generateAlbumUrl('1.jpg');
                            console.warn('扫描失败，添加默认图片');
                        }
                    }

                    console.log(`✅ 本地专辑列表加载完成，共 ${this.albums.length} 张专辑封面`);

                } catch (error) {
                    console.error('加载本地专辑列表失败:', error);
                    // 即使失败也不抛出错误，而是添加一个默认图片
                    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
                    const defaultImage = `${baseUrl}cover/1.jpg`;
                    this.albums = [defaultImage];
                    this.albumUrls[defaultImage] = this.generateAlbumUrl('1.jpg');
                    console.warn('加载失败，使用默认图片');
                }
            }
            
            // 扫描 cover 目录下的所有图片文件
            async scanCoverDirectory(coverDirUrl) {
                try {
                    console.log('开始扫描本地目录:', coverDirUrl);
                    
                    // 首先尝试直接列出所有已知的图片文件
                    const knownFiles = [
                        '1.jpg', '2.jpg', '3.jpg', '4.jpg', '5.jpg', '6.jpg', '7.jpg', '8.jpg'
                    ];
                    
                    let foundImages = 0;
                    
                    // 先添加所有已知的图片文件 
                    for (const filename of knownFiles) {
                        const imageUrl = `${coverDirUrl}${filename}`;
                        this.albums.push(imageUrl);
                        this.albumUrls[imageUrl] = this.generateAlbumUrl(filename);
                        foundImages++;
                    }
                    
                    console.log(`已添加 ${foundImages} 个已知图片文件`);
                    
                    // 然后尝试使用iframe扫描目录
                    try {
                        // 创建一个隐藏的 iframe 来获取目录列表
                        const iframe = document.createElement('iframe');
                        iframe.style.display = 'none';
                        document.body.appendChild(iframe);
                        
                        await new Promise((resolve, reject) => {
                            iframe.onload = () => {
                                try {
                                    // 尝试从 iframe 中获取目录内容
                                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                    
                                    // 查找所有链接，这些链接可能是文件
                                    const links = iframeDoc.querySelectorAll('a');
                                    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
                                    let iframeFounds = 0;
                                    
                                    // 遍历所有链接，寻找图片文件
                                    for (const link of links) {
                                        const href = link.getAttribute('href');
                                        
                                        // 检查是否是图片文件且不在已知文件列表中
                                        if (href && imageExtensions.some(ext => href.toLowerCase().endsWith(ext)) && 
                                            !knownFiles.includes(href)) {
                                            const filename = href;
                                            const imageUrl = `${coverDirUrl}${filename}`;
                                            
                                            // 添加到专辑列表
                                            this.albums.push(imageUrl);
                                            this.albumUrls[imageUrl] = this.generateAlbumUrl(filename);
                                            iframeFounds++;
                                            foundImages++;
                                        }
                                    }
                                    
                                    console.log(`iframe扫描额外找到 ${iframeFounds} 个图片文件`);
                                    
                                    // 清理 iframe
                                    document.body.removeChild(iframe);
                                    resolve();
                                } catch (error) {
                                    if (document.body.contains(iframe)) {
                                        document.body.removeChild(iframe);
                                    }
                                    console.warn('iframe扫描失败:', error);
                                    resolve(); // 即使失败也继续，因为我们已经有了已知文件
                                }
                            };
                            
                            iframe.onerror = () => {
                                if (document.body.contains(iframe)) {
                                    document.body.removeChild(iframe);
                                }
                                console.warn('iframe加载失败');
                                resolve(); // 即使失败也继续，因为我们已经有了已知文件
                            };
                            
                            // 设置 iframe 的 src
                            iframe.src = coverDirUrl;
                            
                            // 如果 3 秒后仍未加载完成，则超时但不报错
                            setTimeout(() => {
                                if (document.body.contains(iframe)) {
                                    document.body.removeChild(iframe);
                                    console.warn('iframe加载超时');
                                    resolve(); // 即使超时也继续，因为我们已经有了已知文件
                                }
                            }, 3000);
                        });
                    } catch (iframeError) {
                        console.warn('iframe扫描出错，但继续使用已知文件:', iframeError);
                    }
                    
                    console.log(`本地目录扫描完成，总共找到 ${foundImages} 个图片文件`);
                    
                    if (foundImages > 0) {
                        return;
                    } else {
                        throw new Error('未找到任何图片文件');
                    }
                } catch (error) {
                    console.error('扫描目录失败:', error);
                    throw error;
                }
            }

            // 更新加载消息
            updateLoadingMessage(message, progress = '') {
                if (this.loadingText) {
                    this.loadingText.textContent = message;
                }
                if (this.loadingProgress) {
                    this.loadingProgress.textContent = progress;
                }
            }

            generateAlbumUrl(filename) {
                // 生成搜索URL的简单逻辑
                const albumName = filename.replace(/\.(jpg|jpeg|png|webp|gif|bmp)$/i, '');
                const cleanName = encodeURIComponent(albumName.replace(/[_\-\d\s()]/g, ' ').trim());

                // 使用Google搜索并添加在线播放关键词
                return `https://www.google.com/search?q=${cleanName}+stream+online+listen`;
            }

            // 初始化主场景的Three.js
            initThreeJS() {
                // 启用Three.js缓存，减少重复解码与上传
                THREE.Cache.enabled = true;

                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x000000);

                // 计算合适的相机参数以填满屏幕
                const aspect = window.innerWidth / window.innerHeight;

                // 使用透视相机以获得真实立体旋转
                this.frustumSize = 20;
                this.fov = 45;
                this.camera = new THREE.PerspectiveCamera(this.fov, aspect, 0.1, 1000);
                // 计算使 z=0 平面可视高度等于 frustumSize 的相机距离
                const distance = this.frustumSize / (2 * Math.tan(THREE.MathUtils.degToRad(this.fov / 2)));
                this.camera.position.set(0, 0, distance);
                this.camera.lookAt(0, 0, 0);

                // 创建渲染器（高性能偏好，降低像素密度、关闭抗锯齿）
                this.renderer = new THREE.WebGLRenderer({ antialias: false, powerPreference: 'high-performance' });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setPixelRatio(Math.min(1.25, window.devicePixelRatio));
                this.container.appendChild(this.renderer.domElement);

                // 由于改用Basic材质，移除光照可减少片段着色开销（如需微弱立体感可再开启）
                // const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
                // this.scene.add(ambientLight);
                // const directionalLight = new THREE.DirectionalLight(0xffffff, 0.2);
                // directionalLight.position.set(0, 0, 5);
                // this.scene.add(directionalLight);

                // 窗口大小调整
                window.addEventListener('resize', () => {
                    const newAspect = window.innerWidth / window.innerHeight;
                    this.camera.aspect = newAspect;
                    this.camera.updateProjectionMatrix();
                    // 维持 z=0 平面的可视高度等于 frustumSize
                    const distance = this.frustumSize / (2 * Math.tan(THREE.MathUtils.degToRad(this.fov / 2)));
                    this.camera.position.set(0, 0, distance);
                    this.renderer.setSize(window.innerWidth, window.innerHeight);

                    // 重新计算网格布局
                    this.recalculateGrid();
                });
                
                // 更新场景背景颜色 - 使用保存的颜色或默认颜色
                this.updateBackgroundColor(this.savedBackgroundColor || '#000000');
            }
            
            // 初始化3D展示台场景
            initShowcaseScene() {
                // 创建展示台场景
                this.showcaseScene = new THREE.Scene();
                // 设置为透明背景，让封面悬浮在底层上
                this.showcaseScene.background = null;
                
                // 设置展示台相机
                const canvas = document.getElementById('album-showcase-canvas');
                const aspect = canvas.clientWidth / canvas.clientHeight;
                this.showcaseCamera = new THREE.PerspectiveCamera(50, aspect, 0.1, 100);
                this.showcaseCamera.position.set(0, 0, 6); // 将相机放在Z轴上，与封面在同一水平线
                this.showcaseCamera.lookAt(0, 0, 0); // 直接看向场景中心
                
                // 创建展示台渲染器
                this.showcaseRenderer = new THREE.WebGLRenderer({ 
                    canvas: canvas,
                    antialias: true, 
                    alpha: true,
                    powerPreference: 'high-performance'
                });
                this.showcaseRenderer.setSize(canvas.clientWidth, canvas.clientHeight);
                
                // 为展示台启用阴影系统
                this.showcaseRenderer.shadowMap.enabled = true;
                this.showcaseRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                // 使用最原始的颜色显示方式，不进行任何颜色空间转换
                // this.showcaseRenderer.outputEncoding = THREE.sRGBEncoding;
                // 启用透明度支持
                this.showcaseRenderer.setClearColor(0x000000, 0);
                
                console.log('3D展示台场景初始化完成（透明背景）');
            }
            
            // 创建展示台几何体和材质（已删除底座）
            createShowcaseStand() {
                // 不再创建底座，保留函数以避免调用错误
                console.log('展示台初始化完成（无底座）');
            }
            
            // 创建专辑封面3D展示网格
            createShowcaseCover(albumPath) {
                // 清理旧的封面（如果存在）
                if (this.showcaseCover) {
                    this.showcaseScene.remove(this.showcaseCover);
                    if (this.showcaseCover.material.map) {
                        this.showcaseCover.material.map.dispose();
                    }
                    this.showcaseCover.material.dispose();
                    this.showcaseCover.geometry.dispose();
                }
                
                // 创建封面几何体（正方形平面，放大尺寸）
                const coverGeometry = new THREE.PlaneGeometry(3.5, 3.5);
                
                // 创建材质（初始为透明，等待纹理加载）- 使用标准材质实现印刷品反射
                const coverMaterial = new THREE.MeshStandardMaterial({ 
                    transparent: true,
                    opacity: 0,
                    side: THREE.DoubleSide,
                    metalness: 0.0,        // 非金属材质，模拟纸质海报
                    roughness: 0.85,       // 适度粗糙度，提供柔和亮面反射
                    emissive: 0x000000,    // 无自发光
                    envMapIntensity: 0.3   // 降低环境映射影响
                });
                
                // 创建封面网格（调整位置使其居中显示）
                this.showcaseCover = new THREE.Mesh(coverGeometry, coverMaterial);
                this.showcaseCover.position.set(0, 0, 0); // 放在场景正中心
                this.showcaseCover.rotation.x = 0; // 去除倾斜，保持垂直
                
                // 为展示台封面启用阴影
                this.showcaseCover.castShadow = true;
                this.showcaseCover.receiveShadow = true;
                
                // 加载纹理
                const textureLoader = new THREE.TextureLoader();
                textureLoader.load(
                    albumPath,
                    (texture) => {
                        // 纹理加载成功
                        this.adjustTextureForShowcase(texture);
                        this.showcaseCover.material.map = texture;
                        this.showcaseCover.material.needsUpdate = true;
                        
                        // 渐显动画
                        this.fadeInShowcaseCover();
                        
                        console.log('展示台封面纹理加载成功');
                    },
                    (progress) => {
                        // 加载进度
                        console.log('纹理加载进度:', Math.round((progress.loaded / progress.total) * 100) + '%');
                    },
                    (error) => {
                        // 加载失败
                        console.error('展示台封面纹理加载失败:', error);
                        // 使用默认颜色
                        this.showcaseCover.material.color.setHex(0x333333);
                        this.fadeInShowcaseCover();
                    }
                );
                
                // 添加到场景
                this.showcaseScene.add(this.showcaseCover);
                
                console.log('展示台封面创建完成');
            }
            
            // 调整展示台纹理（保持方形比例）
            adjustTextureForShowcase(texture) {
                const image = texture.image;
                const imageAspect = image.width / image.height;
                
                if (imageAspect !== 1) {
                    if (imageAspect > 1) {
                        // 宽图：裁切左右两边
                        const cropWidth = image.height;
                        const offsetX = (image.width - cropWidth) / 2 / image.width;
                        texture.offset.x = offsetX;
                        texture.repeat.x = cropWidth / image.width;
                        texture.repeat.y = 1;
                        texture.offset.y = 0;
                    } else {
                        // 高图：裁切上下两边
                        const cropHeight = image.width;
                        const offsetY = (image.height - cropHeight) / 2 / image.height;
                        texture.offset.y = offsetY;
                        texture.repeat.y = cropHeight / image.height;
                        texture.repeat.x = 1;
                        texture.offset.x = 0;
                    }
                }
                
                // 设置纹理过滤和环绕模式
                texture.minFilter = THREE.LinearFilter;
                texture.magFilter = THREE.LinearFilter;
                texture.wrapS = THREE.ClampToEdgeWrap;
                texture.wrapT = THREE.ClampToEdgeWrap;
            }
            
            // 展示台封面渐显动画
            fadeInShowcaseCover() {
                const duration = 800;
                const startTime = Date.now();
                
                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    
                    // 使用缓动函数
                    const easeProgress = progress < 0.5 
                        ? 2 * progress * progress 
                        : 1 - Math.pow(-2 * progress + 2, 2) / 2;
                    
                    this.showcaseCover.material.opacity = easeProgress;
                    
                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        this.showcaseCover.material.opacity = 1;
                    }
                };
                
                animate();
            }
            
            // 设置展示台鼠标交互（全屏追踪）
            setupShowcaseMouseInteraction() {
                const canvas = document.getElementById('album-showcase-canvas');
                if (!canvas) return;
                
                // 全屏鼠标移动事件（绑定到document以支持全屏追踪）
                this.showcaseMouseMoveHandler = (event) => {
                    if (!this.showcaseActive) return;
                    
                    // 基于整个窗口计算鼠标位置
                    const x = event.clientX / window.innerWidth;
                    const y = event.clientY / window.innerHeight;
                    
                    // 将鼠标位置转换为 -1 到 1 的范围，以屏幕中心为基准点
                    this.mousePosition.x = (x - 0.5) * 2 * 1.2; // 适中的敏感度
                    this.mousePosition.y = (y - 0.5) * 2 * 1.2; // 保持原始Y轴映射用于旋转
                    
                    // 限制范围避免过度旋转
                    this.mousePosition.x = Math.max(-1.2, Math.min(1.2, this.mousePosition.x));
                    this.mousePosition.y = Math.max(-1.2, Math.min(1.2, this.mousePosition.y));
                    
                    // 禁用弹簧阻尼效果，避免浮动
                    // 移除所有物理系统的速度施加，确保封面只跟随鼠标位置
                    /*
                    if (this.showcaseCover && this.showcaseCover.userData.physics) {
                        const physics = this.showcaseCover.userData.physics;
                        // 施加与鼠标移动方向相关的力，模拟弹簧反馈
                        physics.velocity.x = -this.mousePosition.x * 0.12;
                        physics.velocity.y = this.mousePosition.y * 0.12; // 增强Y轴效果
                        physics.velocity.z = Math.abs(this.mousePosition.x) * 0.05; // 添加Z轴效果
                        
                        // 增强角速度效果，支持上下反转
                        physics.angularVelocity.x = this.mousePosition.y * 0.08; // 增强绕X轴旋转
                        physics.angularVelocity.y = -this.mousePosition.x * 0.08; // 增强绕Y轴旋转
                        physics.angularVelocity.z = (this.mousePosition.x - this.mousePosition.y) * 0.05; // 添加绕Z轴旋转
                    }
                    */
                };
                
                // 绑定全屏鼠标移动事件
                document.addEventListener('mousemove', this.showcaseMouseMoveHandler);
                
                // 鼠标静止检测（当鼠标长时间不动时复位）
                this.mouseIdleTimer = null;
                this.showcaseMouseIdleHandler = () => {
                    if (this.mouseIdleTimer) {
                        clearTimeout(this.mouseIdleTimer);
                    }
                    this.mouseIdleTimer = setTimeout(() => {
                        this.resetShowcasePosition();
                    }, 60000); // 1分钟后复位
                };
                
                document.addEventListener('mousemove', this.showcaseMouseIdleHandler);
                
                console.log('展示台全屏鼠标交互设置完成');
            }
            
            // 更新展示台封面位置和旋转（根据鼠标位置）
            updateShowcaseCoverTransform() {
                if (!this.showcaseCover || !this.showcaseActive) return;
                
                // 获取物理属性
                const physics = this.showcaseCover.userData.physics;
                const shakeParams = this.showcaseCover.userData.shakeParams;
                
                // 禁用弹簧阻尼物理效果更新，避免浮动
                // 完全移除物理系统更新，确保封面只跟随鼠标
                /*
                if (physics && (Math.abs(physics.velocity.x) > 0.001 || Math.abs(physics.velocity.y) > 0.001 || 
                    Math.abs(physics.angularVelocity.x) > 0.001 || Math.abs(physics.angularVelocity.y) > 0.001)) {
                    // 弹簧-阻尼系统更新
                    if (shakeParams) {
                        // 计算弹簧力 (F = -kx)
                        const springForce = physics.position.clone().multiplyScalar(-shakeParams.springConstant);
                        
                        // 计算阻尼力 (F = -cv)
                        const dampingForce = physics.velocity.clone().multiplyScalar(-shakeParams.damping);
                        
                        // 总力 = 弹簧力 + 阻尼力
                        const totalForce = springForce.add(dampingForce);
                        
                        // 加速度 = 力 / 质量 (a = F/m)
                        physics.acceleration.copy(totalForce).divideScalar(shakeParams.mass);
                        
                        // 更新速度 (v = v0 + a * dt)
                        physics.velocity.add(physics.acceleration.clone().multiplyScalar(0.016)); // 假设60fps
                        
                        // 更新位置 (x = x0 + v * dt)
                        physics.position.add(physics.velocity.clone().multiplyScalar(0.016));
                        
                        // 角运动类似处理
                        // 角弹簧力
                        const angularSpringForce = physics.rotation.clone().multiplyScalar(-shakeParams.springConstant);
                        // 角阻尼力
                        const angularDampingForce = physics.angularVelocity.clone().multiplyScalar(-shakeParams.damping);
                        // 总角力
                        const totalAngularForce = angularSpringForce.add(angularDampingForce);
                        // 角加速度
                        physics.angularAcceleration.copy(totalAngularForce).divideScalar(shakeParams.mass);
                        // 更新角速度
                        physics.angularVelocity.add(physics.angularAcceleration.clone().multiplyScalar(0.016));
                        // 更新角度
                        physics.rotation.add(physics.angularVelocity.clone().multiplyScalar(0.016));
                        
                        // 如果运动已经基本停止，重置速度
                        const velocityMagnitude = physics.velocity.length();
                        const angularVelocityMagnitude = physics.angularVelocity.length();
                        if (velocityMagnitude < 0.001 && angularVelocityMagnitude < 0.001) {
                            physics.velocity.set(0, 0, 0);
                            physics.angularVelocity.set(0, 0, 0);
                        }
                    }
                }
                */
                
                // 计算目标旋转角度（针对透视效应调整上下旋转幅度）
                const maxRotation = Math.PI * 0.15; // 旋转幅度到最大27度
                const targetRotationY = this.mousePosition.x * maxRotation;
                const targetRotationX = this.mousePosition.y * maxRotation * 0.6; // 针对透视视觉效应，减小上下旋转幅度
                
                // 计算目标位置（统一左右和上下的位移幅度，增强位移补偿）
                const maxOffset = 0.35; // 位置位移幅度
                const targetX = this.mousePosition.x * maxOffset;
                const targetY = -this.mousePosition.y * maxOffset; // 反转Y轴位移，鼠标向上时封面向上移动
                const targetZ = this.mousePosition.y * maxOffset * 0.3; // 减少Z轴位移，避免过度前后移动
                
                // 提高响应速度
                const lerpFactor = 0.15; // 提高响应速度
                
                // 更新旋转（统一左右和上下的处理方式）
                // 左右旋转（Y轴）- 使用平滑插值
                this.showcaseCover.rotation.y += (targetRotationY - this.showcaseCover.rotation.y) * lerpFactor;
                // 上下旋转（X轴）- 同样使用平滑插值，保持一致性
                this.showcaseCover.rotation.x += (targetRotationX - this.showcaseCover.rotation.x) * lerpFactor;
                
                // 添加弹簧旋转效果（与鼠标交互叠加）- 减弱强度避免过度晃动
                this.showcaseCover.rotation.x += physics.rotation.x * 0.00; // 减弱X轴旋转效果
                this.showcaseCover.rotation.y += physics.rotation.y * 0.00; // 减弱Y轴旋转效果
                this.showcaseCover.rotation.z = physics.rotation.z * 0.00; // 减弱Z轴旋转效果
                
                // 更新位置（结合鼠标交互和弹簧效果，支持上下反转）- 减弱强度避免过度晃动
                this.showcaseCover.position.x = targetX + physics.position.x * 0.00; // 减弱X轴位移效果
                this.showcaseCover.position.y = targetY + physics.position.y * 0.00; // 减弱Y轴位移控制
                this.showcaseCover.position.z = targetZ + physics.position.z * 0.00; // 减弱Z轴位移效果
            }
            
            // 重置展示台位置
            resetShowcasePosition() {
                this.mousePosition = { x: 0, y: 0 };
                // 强制重置所有旋转轴为0，确保封面在鼠标居中时完全水平
                if (this.showcaseCover) {
                    this.showcaseCover.rotation.x = 0;
                    this.showcaseCover.rotation.y = 0;
                    this.showcaseCover.rotation.z = 0;
                    this.showcaseCover.position.x = 0;
                    this.showcaseCover.position.y = 0;
                    this.showcaseCover.position.z = 0;
                    
                    // 重置物理状态
                    if (this.showcaseCover.userData.physics) {
                        const physics = this.showcaseCover.userData.physics;
                        physics.position.set(0, 0, 0);
                        physics.velocity.set(0, 0, 0);
                        physics.acceleration.set(0, 0, 0);
                        physics.rotation.set(0, 0, 0);
                        physics.angularVelocity.set(0, 0, 0);
                        physics.angularAcceleration.set(0, 0, 0);
                        physics.isShaking = false;
                        physics.shakeStartTime = 0;
                        physics.shakeIntensity = 0;
                    }
                }
                console.log('重置展示台位置和旋转');
            }
            
            // 设置展示台光照系统（专业四光源照明）
            setupShowcaseLights() {
                // 清理旧的灯光
                this.showcaseLights.forEach(light => {
                    this.showcaseScene.remove(light);
                    if (light.target && light.target.parent) {
                        this.showcaseScene.remove(light.target);
                    }
                });
                this.showcaseLights = [];
                
                // 1. 环境光 - 提供基础照明
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.35);
                this.showcaseLights.push(ambientLight);
                this.showcaseScene.add(ambientLight);
                
                // 2. 主光源 - 提供主要亮面反射
                const mainLight = new THREE.DirectionalLight(0xffffff, 0.9);
                mainLight.position.set(4, 6, 3);
                mainLight.target.position.set(0, 0, 0);
                
                // 配置主光源阴影
                mainLight.castShadow = true;
                mainLight.shadow.mapSize.width = 1024;
                mainLight.shadow.mapSize.height = 1024;
                mainLight.shadow.bias = -0.0005;
                
                this.showcaseLights.push(mainLight);
                this.showcaseScene.add(mainLight);
                this.showcaseScene.add(mainLight.target);
                
                // 3. 补充光 - 减少阴影对比度
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.25);
                fillLight.position.set(-3, 2, 2);
                fillLight.target.position.set(0, 0, 0);
                this.showcaseLights.push(fillLight);
                this.showcaseScene.add(fillLight);
                this.showcaseScene.add(fillLight.target);
                
                // 4. 顶部软光 - 模拟天花板漫反射
                const topLight = new THREE.DirectionalLight(0xffffff, 0.1);
                topLight.position.set(0, 10, 0);
                topLight.target.position.set(0, 0, 0);
                this.showcaseLights.push(topLight);
                this.showcaseScene.add(topLight);
                this.showcaseScene.add(topLight.target);
                
                console.log('展示台专业光照系统设置完成（印刷品反射模式）');
            }
            
            // 展示台动画循环
            startShowcaseAnimation() {
                if (this.showcaseAnimationId) {
                    cancelAnimationFrame(this.showcaseAnimationId);
                }
                
                const animate = () => {
                    if (!this.showcaseActive) return;
                    
                    // 更新鼠标跟随交互
                    this.updateShowcaseCoverTransform();
                    
                    // 禁用悬浮动画和弹簧效果，避免浮动
                    // 移除所有自动浮动和物理反馈，让封面完全跟随鼠标
                    /*
                    if (this.showcaseCover) {
                        const time = Date.now() * 0.001;
                        const floatingOffset = Math.sin(time * 1.5) * 0.03;
                        
                        // 获取物理属性
                        const physics = this.showcaseCover.userData.physics;
                        
                        // 应用浮动效果和弹簧反馈（支持上下反转）
                        if (physics) {
                            // 基础Y轴浮动 - 与鼠标控制的Y轴位置结合
                            this.showcaseCover.position.y += (floatingOffset + physics.position.y * 0.4 - this.showcaseCover.position.y) * 0.1;
                            
                            // 弹簧反馈
                            this.showcaseCover.position.x += physics.position.x * 0.15; // 增强X轴反馈
                            this.showcaseCover.position.z += physics.position.z * 0.15; // 增强Z轴反馈
                            
                            // 旋转弹簧反馈（支持上下反转）
                            this.showcaseCover.rotation.x += physics.rotation.x * 0.15; // 增强X轴旋转反馈
                            this.showcaseCover.rotation.y += physics.rotation.y * 0.15; // 增强Y轴旋转反馈
                            this.showcaseCover.rotation.z += physics.rotation.z * 0.1;  // Z轴旋转反馈
                        } else {
                            // 如果没有物理属性，只应用基础浮动
                            this.showcaseCover.position.y = floatingOffset;
                        }
                    }
                    */
                    
                    // 渲染场景
                    this.showcaseRenderer.render(this.showcaseScene, this.showcaseCamera);
                    
                    this.showcaseAnimationId = requestAnimationFrame(animate);
                };
                
                animate();
                console.log('展示台动画循环启动');
            }
            
            // 停止展示台动画
            stopShowcaseAnimation() {
                if (this.showcaseAnimationId) {
                    cancelAnimationFrame(this.showcaseAnimationId);
                    this.showcaseAnimationId = null;
                }
                this.showcaseActive = false;
                console.log('展示台动画停止');
            }
            
            // 初始化3D展示台（完整流程）
            initializeShowcase(albumPath) {
                try {
                    console.log('开始初始化3D展示台:', albumPath);
                    
                    // 检查Canvas元素是否存在
                    const canvas = document.getElementById('album-showcase-canvas');
                    if (!canvas) {
                        console.error('找不到展示台Canvas元素');
                        return;
                    }
                    
                    // 初始化场景
                    this.initShowcaseScene();
                    
                    // 创建展示台
                    this.createShowcaseStand();
                    
                    // 设置光照
                    this.setupShowcaseLights();
                    
                    // 创建封面（如果有专辑路径）
                    if (albumPath) {
                        this.createShowcaseCover(albumPath);
                    }
                    
                    // 初始化物理属性用于磁悬浮晃动效果
                    if (this.showcaseCover) {
                        // 添加物理属性
                        this.showcaseCover.userData.physics = {
                            position: new THREE.Vector3(0, 0, 0),
                            velocity: new THREE.Vector3(0, 0, 0),
                            acceleration: new THREE.Vector3(0, 0, 0),
                            rotation: new THREE.Vector3(0, 0, 0),
                            angularVelocity: new THREE.Vector3(0, 0, 0),
                            angularAcceleration: new THREE.Vector3(0, 0, 0),
                            isShaking: false,
                            shakeStartTime: 0,
                            shakeIntensity: 0
                        };
                        
                        // 调整晃动参数，减弱弹簧回弹效果，增加阻尼稳定性
                        this.showcaseCover.userData.shakeParams = {
                            springConstant: 2.0,    // 降低弹簧常数，减少回弹强度
                            damping: 0.25,          // 增加阻尼系数，快速稳定
                            mass: 1.0,              // 质量保持不变
                            maxShakeIntensity: 0.8  // 降低最大晃动强度
                        };
                    }
                    
                    // 设置鼠标交互
                    this.setupShowcaseMouseInteraction();
                    
                    // 已移除点击事件监听器
                    
                    // 启动动画循环
                    this.showcaseActive = true;
                    this.startShowcaseAnimation();
                    
                    console.log('3D展示台初始化完成');
                    
                } catch (error) {
                    console.error('3D展示台初始化失败:', error);
                    // 失败时清理资源
                    this.cleanupShowcase();
                }
            }
            
            // 清理展示台资源
            cleanupShowcase() {
                try {
                    console.log('开始清理展示台资源');
                    
                    // 停止动画循环
                    this.stopShowcaseAnimation();
                    
                    // 清理全屏鼠标事件监听器
                    if (this.showcaseMouseMoveHandler) {
                        document.removeEventListener('mousemove', this.showcaseMouseMoveHandler);
                        this.showcaseMouseMoveHandler = null;
                    }
                    
                    if (this.showcaseMouseIdleHandler) {
                        document.removeEventListener('mousemove', this.showcaseMouseIdleHandler);
                        this.showcaseMouseIdleHandler = null;
                    }
                    
                    // 清理点击事件监听器
                    const canvas = document.getElementById('album-showcase-canvas');
                    if (canvas) {
                        canvas.removeEventListener('click', this.showcaseClickHandler);
                    }
                    
                    // 清理鼠标静止定时器
                    if (this.mouseIdleTimer) {
                        clearTimeout(this.mouseIdleTimer);
                        this.mouseIdleTimer = null;
                    }
                    
                    // 清理封面资源
                    if (this.showcaseCover) {
                        // 清理物理属性
                        if (this.showcaseCover.userData.physics) {
                            this.showcaseCover.userData.physics = null;
                        }
                        if (this.showcaseCover.userData.shakeParams) {
                            this.showcaseCover.userData.shakeParams = null;
                        }
                        
                        // 清理纹理
                        if (this.showcaseCover.material.map) {
                            this.showcaseCover.material.map.dispose();
                        }
                        // 清理材质
                        this.showcaseCover.material.dispose();
                        // 清理几何体
                        this.showcaseCover.geometry.dispose();
                        // 从场景中移除
                        if (this.showcaseScene && this.showcaseCover.parent) {
                            this.showcaseScene.remove(this.showcaseCover);
                        }
                        this.showcaseCover = null;
                    }
                    
                    // 清理展示台资源
                    if (this.showcaseStand) {
                        this.showcaseStand.children.forEach(child => {
                            if (child.geometry) child.geometry.dispose();
                            if (child.material) child.material.dispose();
                        });
                        if (this.showcaseScene) {
                            this.showcaseScene.remove(this.showcaseStand);
                        }
                        this.showcaseStand = null;
                    }
                    
                    // 清理灯光资源
                    this.showcaseLights.forEach(light => {
                        if (this.showcaseScene) {
                            this.showcaseScene.remove(light);
                            // 清理定向光的target
                            if (light.target && light.target.parent) {
                                this.showcaseScene.remove(light.target);
                            }
                        }
                    });
                    this.showcaseLights = [];
                    
                    // 清理渲染器
                    if (this.showcaseRenderer) {
                        this.showcaseRenderer.dispose();
                        this.showcaseRenderer = null;
                    }
                    
                    // 清理场景
                    if (this.showcaseScene) {
                        // 递归清理场景中的所有对象
                        this.showcaseScene.traverse((object) => {
                            if (object.geometry) {
                                object.geometry.dispose();
                            }
                            if (object.material) {
                                if (Array.isArray(object.material)) {
                                    object.material.forEach(material => material.dispose());
                                } else {
                                    object.material.dispose();
                                }
                            }
                        });
                        this.showcaseScene = null;
                    }
                    
                    // 重置相机
                    this.showcaseCamera = null;
                    
                    // 重置状态
                    this.showcaseActive = false;
                    this.mousePosition = { x: 0, y: 0 };
                    
                    console.log('展示台资源清理完成');
                    
                } catch (error) {
                    console.error('清理展示台资源时出错:', error);
                    // 强制重置状态
                    this.showcaseActive = false;
                    this.showcaseScene = null;
                    this.showcaseCamera = null;
                    this.showcaseRenderer = null;
                    this.showcaseCover = null;
                    this.showcaseStand = null;
                    this.showcaseLights = [];
                    this.mousePosition = { x: 0, y: 0 };
                }
            }

            createGrid() {
                this.calculateGridLayout();

                // 重置已使用的专辑列表
                this.usedAlbums = [];

                // 使用Math.ceil确保覆盖整个屏幕，包括部分显示的封面
                const totalColumns = Math.ceil(this.gridCols);
                const totalCovers = this.gridRows * totalColumns;
                console.log(`开始创建网格: ${this.gridRows}x${totalColumns} = ${totalCovers} 个封面`);
                console.log(`可用专辑数量: ${this.albums.length}`);

                if (totalCovers > this.albums.length) {
                    console.log(`需要重复使用 ${totalCovers - this.albums.length} 个封面`);
                } else {
                    console.log(`专辑数量足够，无需重复`);
                }

                for (let row = 0; row < this.gridRows; row++) {
                    for (let col = 0; col < totalColumns; col++) {
                        const x = this.startX + col * (this.coverSize + this.gap) + this.coverSize / 2;
                        const y = this.startY - row * (this.coverSize + this.gap) - this.coverSize / 2;

                        this.createCover(x, y, row, col);
                    }
                }

                // console.log(`网格创建完成，实际使用了 ${this.usedAlbums.length} 个不同的专辑`);
            }

            calculateGridLayout() {
                // 获取相机的可视区域
                const aspect = window.innerWidth / window.innerHeight;
                const frustumSize = this.frustumSize;
                const viewWidth = frustumSize * aspect;
                const viewHeight = frustumSize;

                // 根据设置的行数计算封面大小
                // 封面大小 = (视口高度 - (行数-1) * 间隙) / 行数
                this.coverSize = (viewHeight - (this.gridRows - 1) * this.gap) / this.gridRows;
                
                // 计算能容纳的列数（可能是小数，这样两侧可能会出现不足一个封面的情况）
                this.gridCols = viewWidth / (this.coverSize + this.gap);
                
                // 计算实际使用的总尺寸
                const actualWidth = Math.ceil(this.gridCols) * (this.coverSize + this.gap) - this.gap;
                const actualHeight = this.gridRows * this.coverSize + (this.gridRows - 1) * this.gap;

                // 计算起始位置以居中显示
                this.startX = -actualWidth / 2;
                this.startY = actualHeight / 2;

                // console.log(`网格布局: ${this.gridCols.toFixed(2)}x${this.gridRows}, 封面大小: ${this.coverSize.toFixed(3)}, 间隙: ${this.gap}`);
                // console.log(`视图尺寸: ${viewWidth.toFixed(2)} x ${viewHeight.toFixed(2)}`);
                // console.log(`实际尺寸: ${actualWidth.toFixed(2)} x ${actualHeight.toFixed(2)}`);
            }

            recalculateGrid() {
                // 清除现有的封面
                this.covers.forEach(cover => {
                    this.scene.remove(cover);
                });
                this.covers = [];

                // 标记为重新初始化状态
                this.isInitializing = true;

                // 重新创建网格
                this.createGrid();

                // 重新初始化完成
                this.isInitializing = false;
            }

            createCover(x, y, row, col) {
                // 创建几何体 - 使用计算出的封面大小
                const geometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);

                // 加载随机专辑封面纹理
                const albumPath = this.getRandomAlbum();
                const textureLoader = new THREE.TextureLoader();
                const texture = textureLoader.load(albumPath, (loadedTexture) => {
                    // 图片加载完成后，调整纹理以实现 object-fit: cover 效果
                    this.adjustTextureForCover(loadedTexture);
                });

                // 创建材质
                const material = new THREE.MeshBasicMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide
                });

                // 创建网格
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.set(x, y, 0);

                // 存储网格信息
                mesh.userData = {
                    row: row,
                    col: col,
                    index: row * this.gridCols + col,
                    albumPath: albumPath,
                    originalPosition: { x, y, z: 0 },
                    originalRotation: { x: 0, y: 0, z: 0 }
                };

                this.scene.add(mesh);
                this.covers.push(mesh);
            }

            getRandomAlbum() {
                // 检查专辑数组是否为空
                if (this.albums.length === 0) {
                    // 如果专辑数组为空，返回本地cover目录下的默认图片路径
                    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
                    const defaultImage = `${baseUrl}cover/1.jpg`;
                    return defaultImage;
                }
                
                if (this.isInitializing) {
                    // 初始化时优先使用不同的封面
                    return this.getUniqueAlbumForInitialization();
                } else {
                    // 动画时使用完全随机的封面
                    return this.albums[Math.floor(Math.random() * this.albums.length)];
                }
            }

            getUniqueAlbumForInitialization() {
                // 检查专辑数组是否为空
                if (this.albums.length === 0) {
                    // 如果专辑数组为空，返回本地cover目录下的默认图片路径
                    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
                    const defaultImage = `${baseUrl}cover/1.jpg`;
                    return defaultImage;
                }
                
                // 如果还有未使用的专辑，优先选择
                if (this.usedAlbums.length < this.albums.length) {
                    let albumIndex;
                    do {
                        albumIndex = Math.floor(Math.random() * this.albums.length);
                    } while (this.usedAlbums.includes(albumIndex));

                    this.usedAlbums.push(albumIndex);

                    // 只在前几个和达到里程碑时输出日志
                    if (this.usedAlbums.length <= 5 || this.usedAlbums.length % 10 === 0) {
                        const fileName = this.albums[albumIndex].split('/').pop();
                        console.log(`✓ 新封面 ${this.usedAlbums.length}/${this.albums.length}: ${fileName}`);
                    }

                    return this.albums[albumIndex];
                } else {
                    // 所有专辑都用过了，开始重复使用
                    const albumIndex = Math.floor(Math.random() * this.albums.length);

                    // 第一次开始重复时输出提示
                    if (this.usedAlbums.length === this.albums.length) {
                        console.log(`📋 所有 ${this.albums.length} 个不同封面已使用完，开始重复使用`);
                        this.usedAlbums.push(-1); // 标记已开始重复
                    }

                    return this.albums[albumIndex];
                }
            }

            // 调整纹理以实现 object-fit: cover 效果
            adjustTextureForCover(texture) {
                const image = texture.image;
                if (!image || !image.width || !image.height) {
                    return;
                }

                const imageAspect = image.width / image.height;

                if (imageAspect !== 1) { // 不是正方形
                    // 计算裁切参数以实现居中裁切
                    if (imageAspect > 1) {
                        // 宽图：裁切左右两边
                        const cropWidth = image.height; // 使用高度作为裁切宽度
                        const offsetX = (image.width - cropWidth) / 2 / image.width;

                        texture.offset.x = offsetX;
                        texture.repeat.x = cropWidth / image.width;
                        texture.offset.y = 0;
                        texture.repeat.y = 1;
                    } else {
                        // 高图：裁切上下两边
                        const cropHeight = image.width; // 使用宽度作为裁切高度
                        const offsetY = (image.height - cropHeight) / 2 / image.height;

                        texture.offset.x = 0;
                        texture.repeat.x = 1;
                        texture.offset.y = offsetY;
                        texture.repeat.y = cropHeight / image.height;
                    }

                    texture.needsUpdate = true;
                }
            }

            startAnimations() {
                if (this.animationInterval) {
                    clearInterval(this.animationInterval);
                }
                
                this.animationInterval = setInterval(() => {
                    this.triggerRandomAnimation();
                }, Math.random() * 1000 + this.animationIntervalTime);
            }

            triggerRandomAnimation() {
                // 选择一种动画类型
                const animations = ['flip', 'drop', 'linkedDrop', 'rollDrop', 'pinRotation', 'rowRollDrop', 'rowDrop'];
                const weights = [
                    this.animationWeights.flip,
                    this.animationWeights.drop,
                    this.animationWeights.linkedDrop,
                    this.animationWeights.rollDrop,
                    this.animationWeights.pinRotation,
                    this.animationWeights.rowRollDrop,
                    this.animationWeights.rowDrop
                ];
                const selectedAnimation = this.getWeightedRandomAnimation(animations, weights);
                
                // 对于行动画类型，直接执行行动画
                if (selectedAnimation === 'rowRollDrop' || selectedAnimation === 'rowDrop') {
                    this.playAnimation(null, selectedAnimation);
                    return;
                }
                
                // 对于普通动画，随机选择3-6个封面
                const animationCount = Math.floor(Math.random() * 4) + 3;

                // 随机选择封面，确保不重复
                const selectedCovers = [];
                const availableCovers = [...this.covers];

                for (let i = 0; i < animationCount && availableCovers.length > 0; i++) {
                    const randomIndex = Math.floor(Math.random() * availableCovers.length);
                    selectedCovers.push(availableCovers.splice(randomIndex, 1)[0]);
                }

                // 序列触发动画
                selectedCovers.forEach((cover, index) => {
                    setTimeout(() => {
                        this.playAnimation(cover, selectedAnimation);
                    }, index * 200);
                });
            }

            getWeightedRandomAnimation(animations, weights) {
                const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
                let random = Math.random() * totalWeight;

                for (let i = 0; i < animations.length; i++) {
                    random -= weights[i];
                    if (random <= 0) {
                        return animations[i];
                    }
                }

                return animations[0];
            }

            playAnimation(cover, type) {
                switch (type) {
                    case 'flip':
                        this.playFlipAnimation(cover);
                        break;
                    case 'drop':
                        this.playDropAnimation(cover);
                        break;
                    case 'linkedDrop':
                        this.playLinkedDropAnimation(cover);
                        break;
                    case 'rollDrop':
                        this.playRollDropAnimation(cover);
                        break;
                    case 'pinRotation':
                        this.playPinRotationAnimation(cover);
                        break;
                    case 'rowRollDrop':
                        this.playRowRollDropAnimation();
                        break;
                    case 'rowDrop':
                        this.playRowDropAnimation();
                        break;
                }
            }

            playFlipAnimation(cover) {
                // 检查封面是否正在进行其他动画
                if (cover.userData.isAnimating) {
                    // console.log('封面正在动画中，跳过翻转动画');
                    return;
                }

                // 标记为动画中
                cover.userData.isAnimating = true;

                // 选择一个不同于当前的专辑作为“背面/翻过来后”的新封面
                const currentAlbum = cover.userData.albumPath;
                let newAlbum = this.getRandomAlbum();
                let tries = 0;
                while (newAlbum === currentAlbum && tries < 8) {
                    newAlbum = this.getRandomAlbum();
                    tries++;
                }

                // 先加载好新纹理，加载完成后再开始翻转（避免中途切贴图造成顿挫）
                const loader = new THREE.TextureLoader();
                loader.load(
                    newAlbum,
                    (newTexture) => {
                        // 调整纹理以实现正方形裁切
                        this.adjustTextureForCover(newTexture);
                        // 创建“正反两面”结构：用一个临时 Group 承载两张面
                        const flipGroup = new THREE.Group();
                        flipGroup.position.copy(cover.position);
                        flipGroup.rotation.set(0, 0, 0);

                        // 把原 cover 作为“正面”放进组里，并将其局部变换重置
                        const oldSide = cover.material.side;
                        cover.material.side = THREE.FrontSide; // 正面只渲染朝向摄像机的一面
                        cover.position.set(0, 0, 0);
                        cover.rotation.set(0, 0, 0);
                        flipGroup.add(cover);

                        // 创建“背面”：几何体绕Y轴旋转180°，这样在旋转过半后显示为正向、无镜像
                        const backGeometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);
                        backGeometry.rotateY(Math.PI);
                        const backMaterial = new THREE.MeshBasicMaterial({
                            map: newTexture,
                            transparent: true,
                            side: THREE.FrontSide
                        });
                        const backMesh = new THREE.Mesh(backGeometry, backMaterial);
                        backMesh.position.set(0, 0, 0);
                        flipGroup.add(backMesh);

                        // 将组加入场景，并临时从场景中移除 cover（但 covers 数组引用保持不变）
                        this.scene.add(flipGroup);
                        this.scene.remove(cover);

                        // 动画参数 + 缓动
                        const duration = 900;
                        const startTime = Date.now();
                        const easeInOutSine = (t) => 0.5 - 0.5 * Math.cos(Math.PI * t);

                        const animate = () => {
                            const elapsed = Date.now() - startTime;
                            const progress = Math.min(elapsed / duration, 1);
                            const eased = easeInOutSine(progress);

                            // 围绕世界Z轴垂直的自身Y轴翻转
                            flipGroup.rotation.y = eased * Math.PI;

                            if (progress < 1) {
                                requestAnimationFrame(animate);
                            } else {
                                // 翻转完成：将原 cover 的贴图更新为新封面
                                cover.material.map = newTexture;
                                cover.material.needsUpdate = true;
                                cover.userData.albumPath = newAlbum;

                                // 从组里取回 cover，恢复到原位置与旋转基线
                                cover.position.copy(flipGroup.position);
                                cover.rotation.set(0, 0, 0);

                                // 清理临时对象
                                this.scene.remove(flipGroup);
                                this.scene.add(cover);
                                backGeometry.dispose();
                                backMaterial.dispose();

                                // 恢复原有的 side 设置
                                cover.material.side = oldSide;

                                // 结束动画标记
                                cover.userData.isAnimating = false;
                            }
                        };

                        animate();
                    },
                    undefined,
                    (err) => {
                        // console.warn('翻转新纹理加载失败:', err);
                        cover.userData.isAnimating = false;
                    }
                );
            }

            playDropAnimation(cover) {
                // 检查封面是否正在进行其他动画
                if (cover.userData.isAnimating) {
                    // console.log('封面正在动画中，跳过掉落动画');
                    return;
                }

                // 标记为动画中
                cover.userData.isAnimating = true;

                // 保存当前状态
                const originalX = cover.position.x;
                const originalY = cover.position.y;
                const originalZ = cover.position.z;
                const currentAlbum = cover.userData.albumPath;

                // 预先准备新封面 - 在动画开始前就创建并放置在原位置
                const newAlbum = this.getRandomAlbum();
                const textureLoader = new THREE.TextureLoader();
                const newTexture = textureLoader.load(newAlbum, (loadedTexture) => {
                    this.adjustTextureForCover(loadedTexture);
                });

                // 创建新封面几何体和材质
                const newGeometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);
                const newMaterial = new THREE.MeshBasicMaterial({
                    map: newTexture,
                    transparent: true,
                    side: THREE.DoubleSide
                });

                // 创建新封面网格，放在原封面后面（z轴稍微靠后）
                const newCover = new THREE.Mesh(newGeometry, newMaterial);
                newCover.position.set(originalX, originalY, -0.01);
                newCover.userData = {
                    row: cover.userData.row,
                    col: cover.userData.col,
                    index: cover.userData.index,
                    albumPath: newAlbum,
                    originalPosition: { ...cover.userData.originalPosition },
                    originalRotation: { ...cover.userData.originalRotation }
                };
                this.scene.add(newCover);

                // 使用“中心X轴（上下边中线）”作为枢轴，进行立体旋转并自然下坠
                const pivot = new THREE.Group();
                pivot.position.set(originalX, originalY, originalZ);
                this.scene.add(pivot);

                // 由 pivot 控制旋转/位移；cover 在局部坐标保持居中
                cover.rotation.set(0, 0, 0);
                cover.position.set(0, 0, 0);
                pivot.add(cover);

                // 动画参数
                const frustumSize = 20;
                const viewHeight = frustumSize;
                const finalPivotY = -viewHeight / 2 - this.coverSize; // 最终落到屏幕底部之外
                const duration = 2200; // 增加持续时间，让动画更自然
                const startTime = Date.now();


                // 物理参数
                const gravity = 9.8; // 重力加速度（相对单位）
                const initialVelocityY = 0; // 初始垂直速度
                const airResistance = 0.02; // 空气阻力系数
                const terminalVelocity = 8; // 终端速度
                // 随机一些三维姿态参数，让掉落不显得“压扁”
                const yawTarget = (Math.random() * 0.8 - 0.4); // 绕Y轴的目标偏航，-23°~23°
                const zForward = Math.max(0.8, this.coverSize * 0.5); // 向屏幕内外推进的幅度
                const rollWobble = (Math.random() * 0.3 - 0.15); // 绕Z轴的抖动
                const baseTiltX = 0.25; // 第一阶段前倾角

                // 旋转参数 - 改进的物理模拟
                const initialRotationSpeed = 0.5; // 初始旋转速度（弧度/秒）
                const rotationAcceleration = 2.5; // 旋转加速度
                const maxRotationSpeed = 12; // 最大旋转速度
                const rotationAxis = Math.random() > 0.5 ? 1 : -1; // 随机旋转方向

                const zClearance = 0.08; // 与“墙”的最小前向间距

                // 改进的缓动函数
                const easeInQuart = (t) => t * t * t * t;
                const easeOutQuart = (t) => 1 - Math.pow(1 - t, 4);
                const easeInOutCubic = (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    if (progress <= 0.2) {
                        // 阶段1：准备阶段 - 轻微前倾和分离
                        const p = progress / 0.2;
                        const tilt = p * baseTiltX;

                        pivot.rotation.x = tilt * easeOutQuart(p);
                        pivot.rotation.y = yawTarget * (0.3 * easeInOutCubic(p));
                        pivot.rotation.z = rollWobble * 0.2 * Math.sin(p * Math.PI);

                        // 轻微上升然后开始下落
                        const lift = Math.sin(p * Math.PI) * 0.1;
                        pivot.position.y = originalY + lift;

                        // Z轴分离
                        const half = this.coverSize / 2;
                        const minZ = zClearance + half * Math.abs(Math.sin(tilt));
                        const baseZ = originalZ + p * 0.2;
                        pivot.position.z = Math.max(baseZ, minZ);
                    } else {
                        // 阶段2：绕中心X轴持续旋转 + 偏航完成 + 下坠 + 轻微滚动抖动
                        const p = (progress - 0.2) / 0.8;
                        const fallTime = p * (duration * 0.8 / 1000); // 掉落时间（秒）

                        // 改进的重力模拟
                        let velocityY = initialVelocityY + gravity * fallTime;
                        // 应用空气阻力
                        velocityY = Math.min(velocityY * (1 - airResistance * fallTime), terminalVelocity);

                        // 改进的旋转物理模拟
                        let currentRotationSpeed = initialRotationSpeed + rotationAcceleration * fallTime;
                        currentRotationSpeed = Math.min(currentRotationSpeed, maxRotationSpeed);

                        // 旋转角度累积
                        const totalRotation = (initialRotationSpeed * fallTime + 0.5 * rotationAcceleration * fallTime * fallTime) * rotationAxis;
                        pivot.rotation.x = baseTiltX + totalRotation;

                        // Y轴偏航渐进到目标，避免仅“压扁”视觉
                        const yawProgress = easeInOutCubic(Math.min(p * 1.5, 1));
                        pivot.rotation.y = yawTarget * (0.3 + 0.7 * yawProgress);

                        // Z轴抖动 - 模拟空气湍流
                        const turbulence = Math.sin(fallTime * 8) * Math.cos(fallTime * 3) * 0.1;
                        pivot.rotation.z = rollWobble * (Math.sin(p * Math.PI * 2) + turbulence) * Math.min(p * 2, 1);

                        // 使用物理公式计算位置：s = v₀t + ½gt²
                        const fallDistance = initialVelocityY * fallTime + 0.5 * gravity * fallTime * fallTime;
                        // 应用空气阻力修正
                        const resistanceModifier = 1 - Math.min(airResistance * fallTime, 0.3);
                        const actualFallDistance = fallDistance * resistanceModifier;

                        pivot.position.y = originalY - actualFallDistance * (Math.abs(originalY - finalPivotY) / 10);

                        // Z轴推进 - 增强透视效果
                        const half = this.coverSize / 2;
                        const baseZ = originalZ + 0.2 + easeInQuart(p) * zForward;
                        const minZ = zClearance + half * Math.abs(Math.sin(pivot.rotation.x));
                        pivot.position.z = Math.max(baseZ, minZ);

                        // 改进的淡出效果
                        if (p > 0.6) {
                            const fadeStart = 0.6;
                            const fadeProgress = (p - fadeStart) / (1 - fadeStart);
                            const opacity = 1 - easeInQuart(fadeProgress);
                            cover.material.opacity = Math.max(0, opacity);
                        }

                        // 轻微的尺寸变化模拟距离感
                        const scaleEffect = 1 + easeInQuart(p) * 0.15;
                        cover.scale.set(scaleEffect, scaleEffect, scaleEffect);
                    }

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 动画结束：移除旧封面与枢轴组
                        pivot.remove(cover);
                        this.scene.remove(pivot);
                        this.scene.remove(cover);

                        // 用新封面顶替
                        newCover.position.z = 0;
                        const coverIndex = this.covers.indexOf(cover);
                        if (coverIndex !== -1) {
                            this.covers[coverIndex] = newCover;
                        }

                        // 清除动画标记
                        newCover.userData.isAnimating = false;
                    }
                };

                animate();
            }

            playLinkedDropAnimation(cover) {
                // 找到一个距离是4的倍数的目标位置
                const sourceIndex = cover.userData.index;
                const sourceRow = Math.floor(sourceIndex / this.gridCols);
                const sourceCol = sourceIndex % this.gridCols;

                // 寻找合适的目标：距离是4的倍数
                const validTargets = [];

                for (const targetCover of this.covers) {
                    if (targetCover === cover) continue;

                    const targetIndex = targetCover.userData.index;
                    const targetRow = Math.floor(targetIndex / this.gridCols);
                    const targetCol = targetIndex % this.gridCols;

                    // 计算曼哈顿距离
                    const distance = Math.abs(targetRow - sourceRow) + Math.abs(targetCol - sourceCol);

                    // 只选择距离是4的倍数的目标，且在合理范围内
                    if (distance % 4 === 0 && distance >= 4 && distance <= 12 && targetRow > sourceRow) {
                        validTargets.push({
                            cover: targetCover,
                            distance: distance,
                            row: targetRow,
                            col: targetCol
                        });
                    }
                }

                if (validTargets.length === 0) {
                    // 如果没有合适的4倍数距离目标，执行普通掉落
                    // console.log('没有找到4倍数距离的目标，执行掉落动画');
                    this.playDropAnimation(cover);
                    return;
                }

                // 选择一个目标（优先选择距离较近的）
                validTargets.sort((a, b) => a.distance - b.distance);
                const selectedTarget = validTargets[Math.floor(Math.random() * Math.min(3, validTargets.length))];

                // console.log(`选择目标: 距离${selectedTarget.distance}步 (${selectedTarget.distance * 90}度)`);

                // 计算简单直接的路径
                const path = this.calculateDirectPath(sourceRow, sourceCol, selectedTarget.row, selectedTarget.col);

                // 执行滚落动画
                this.executeRollingAnimation(cover, selectedTarget.cover, path);
            }

            calculateDirectPath(sourceRow, sourceCol, targetRow, targetCol) {
                const path = [];
                let currentRow = sourceRow;
                let currentCol = sourceCol;

                // 简单策略：先水平移动，再垂直移动
                // 因为我们已经确保了总距离是4的倍数，所以不需要复杂的调整

                // 水平移动
                while (currentCol !== targetCol) {
                    if (currentCol < targetCol) {
                        currentCol++;
                    } else {
                        currentCol--;
                    }
                    path.push({
                        row: currentRow,
                        col: currentCol,
                        index: currentRow * this.gridCols + currentCol
                    });
                }

                // 垂直移动
                while (currentRow !== targetRow) {
                    if (currentRow < targetRow) {
                        currentRow++;
                    } else {
                        currentRow--;
                    }
                    path.push({
                        row: currentRow,
                        col: currentCol,
                        index: currentRow * this.gridCols + currentCol
                    });
                }

                // console.log(`直接路径: ${path.length}步 (${path.length * 90}度旋转)`);
                return path;
            }



            executeRollingAnimation(sourceCover, targetCover, path) {
                // 检查封面是否正在进行其他动画
                if (sourceCover.userData.isAnimating || targetCover.userData.isAnimating) {
                    // console.log(`滚动动画跳过: 源封面(${sourceCover.userData.row},${sourceCover.userData.col})动画状态=${sourceCover.userData.isAnimating}, 目标封面(${targetCover.userData.row},${targetCover.userData.col})动画状态=${targetCover.userData.isAnimating}`);
                    return;
                }

                const sourceAlbum = sourceCover.userData.albumPath;

                // 记录源位置索引，供结束时更新 covers 数组
                const originalRow = sourceCover.userData.row;
                const originalCol = sourceCover.userData.col;
                const originalIndex = sourceCover.userData.index;

                // 在 A 点（源位置）预先创建一个新封面，避免出现黑色镂空
                const textureLoader = new THREE.TextureLoader();
                const newAlbumAtSource = this.getRandomAlbum();
                const newTextureAtSource = textureLoader.load(newAlbumAtSource, (loadedTexture) => {
                    this.adjustTextureForCover(loadedTexture);
                });

                const newGeometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);
                const newMaterialAtSource = new THREE.MeshBasicMaterial({
                    map: newTextureAtSource,
                    transparent: true,
                    side: THREE.DoubleSide
                });

                const newSourceCover = new THREE.Mesh(newGeometry, newMaterialAtSource);
                newSourceCover.position.set(sourceCover.position.x, sourceCover.position.y, -0.01);
                newSourceCover.userData = {
                    row: originalRow,
                    col: originalCol,
                    index: originalIndex,
                    albumPath: newAlbumAtSource,
                    originalPosition: { ...sourceCover.userData.originalPosition },
                    originalRotation: { ...sourceCover.userData.originalRotation }
                };
                this.scene.add(newSourceCover);

                // 设置动画状态
                sourceCover.userData.isRolling = true;
                sourceCover.userData.isAnimating = true;
                targetCover.userData.isAnimating = true; // 锁定目标，避免被其它动画占用

                // 提升滚动封面的Z轴层级，避免与其他封面重叠
                sourceCover.position.z = 0.1;

                let totalRotation = 0;
                // 使用精确的网格位置作为起始位置
                let currentX = this.startX + originalCol * (this.coverSize + this.gap) + this.coverSize / 2;
                let currentY = this.startY - originalRow * (this.coverSize + this.gap) - this.coverSize / 2;
                const stepDuration = 300; // 每步的持续时间

                // 创建完整的路径，包括起始位置
                const fullPath = [
                    { row: originalRow, col: originalCol },
                    ...path
                ];

                let currentStep = 0;

                const animateStep = () => {
                    if (currentStep >= fullPath.length - 1) {
                        // 动画完成
                        this.finishRollingAnimation(sourceCover, targetCover, newSourceCover, originalRow, originalCol, originalIndex, totalRotation);
                        return;
                    }

                    const currentPoint = fullPath[currentStep];
                    const nextPoint = fullPath[currentStep + 1];

                    // 计算移动方向
                    const deltaRow = nextPoint.row - currentPoint.row;
                    const deltaCol = nextPoint.col - currentPoint.col;

                    // 计算目标位置 - 使用精确的网格位置
                    const targetGridX = this.startX + nextPoint.col * (this.coverSize + this.gap) + this.coverSize / 2;
                    const targetGridY = this.startY - nextPoint.row * (this.coverSize + this.gap) - this.coverSize / 2;

                    const targetX = targetGridX;
                    const targetY = targetGridY;

                    // 计算旋转角度 - 正方形滚动的正确方向
                    let rotationIncrement = 0;
                    if (deltaCol > 0) {
                        // 向右滚动：正方形向前滚动（顺时针）
                        rotationIncrement = -90; // 注意：Three.js中Z轴旋转，负值是顺时针
                    } else if (deltaCol < 0) {
                        // 向左滚动：正方形向后滚动（逆时针）
                        rotationIncrement = 90;
                    } else if (deltaRow > 0) {
                        // 向下滚动：正方形向前滚动（顺时针）
                        rotationIncrement = -90;
                    } else if (deltaRow < 0) {
                        // 向上滚动：正方形向后滚动（逆时针）
                        rotationIncrement = 90;
                    }

                    totalRotation += rotationIncrement;

                    // 执行滚动动画
                    this.animateRollingStep(sourceCover, currentX, currentY, targetX, targetY, totalRotation, rotationIncrement, stepDuration);

                    // 更新当前位置
                    currentX = targetX;
                    currentY = targetY;

                    currentStep++;
                    setTimeout(animateStep, stepDuration);
                };

                // 开始动画
                animateStep();
            }

            animateRollingStep(cover, startX, startY, targetX, targetY, finalRotation, rotationIncrement, duration) {
                const startTime = Date.now();
                const startRotation = finalRotation - rotationIncrement;

                // 正方形滚动：简化版本
                // 正方形滚动一格的距离等于边长
                const sideLength = this.coverSize;

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // 使用缓动函数让滚动更自然
                    const easeProgress = progress < 0.5
                        ? 2 * progress * progress
                        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

                    // 线性插值位置
                    const currentX = startX + (targetX - startX) * easeProgress;
                    const currentY = startY + (targetY - startY) * easeProgress;

                    // 旋转角度
                    const currentRotation = startRotation + rotationIncrement * easeProgress;

                    // 滚动时的轻微弧形轨迹
                    const arcHeight = sideLength * 0.1 * Math.sin(easeProgress * Math.PI);

                    // 应用变换
                    cover.position.x = currentX;
                    cover.position.y = currentY + arcHeight; // 轻微的弧形
                    cover.position.z = 0.1 + arcHeight * 0.5; // 保持在其他封面之上
                    cover.rotation.z = currentRotation * Math.PI / 180;

                    // 滚动时轻微放大
                    const scale = 1 + arcHeight / sideLength * 0.5;
                    cover.scale.set(scale, scale, scale);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 确保最终状态准确，立即重置所有效果
                        cover.position.x = targetX;
                        cover.position.y = targetY;
                        cover.position.z = 0.1; // 保持在上层直到动画完全结束
                        cover.rotation.z = finalRotation * Math.PI / 180;
                        cover.scale.set(1, 1, 1); // 立即重置缩放，不要任何放大效果
                    }
                };

                animate();
            }

            finishRollingAnimation(sourceCover, targetCover, newSourceCover, originalRow, originalCol, originalIndex, totalRotation) {
                // 路径是4的倍数，totalRotation应为360的倍数；直接归零
                // console.log(`滚动完成，总旋转: ${totalRotation}度，归零并完成交换`);

                // 立即设置最终状态
                sourceCover.rotation.z = 0;

                // 将源封面移动到目标位置（接管 B 点）
                sourceCover.position.set(targetCover.position.x, targetCover.position.y, 0);
                sourceCover.userData.row = targetCover.userData.row;
                sourceCover.userData.col = targetCover.userData.col;
                sourceCover.userData.index = targetCover.userData.index;

                // 移除旧的目标封面
                this.scene.remove(targetCover);

                // 将 A 点的新封面提到正常层级
                newSourceCover.position.z = 0;
                newSourceCover.userData.isAnimating = false;
                newSourceCover.userData.isRolling = false;

                // 更新 covers 数组映射：
                const targetIndexInArray = this.covers.indexOf(targetCover);
                const sourceIndexInArray = this.covers.indexOf(sourceCover);
                if (sourceIndexInArray !== -1) {
                    // 用 A 点的新封面顶替原来的 sourceCover 槽位
                    this.covers[sourceIndexInArray] = newSourceCover;
                }
                if (targetIndexInArray !== -1) {
                    // 用移动过来的 sourceCover 顶替目标槽位
                    this.covers[targetIndexInArray] = sourceCover;
                }

                // 重置移动封面的状态
                sourceCover.userData.isRolling = false;
                sourceCover.userData.isAnimating = false;
                sourceCover.position.z = 0;
                sourceCover.scale.set(1, 1, 1);
                sourceCover.material.opacity = 1;

                // console.log(`完成：A 点已补上新封面 (${originalRow},${originalCol})，B 点由源封面接管 (${sourceCover.userData.row},${sourceCover.userData.col})`);
            }

            playRollDropAnimation(cover) {
                // 检查封面是否正在进行其他动画
                if (cover.userData.isAnimating) {
                    // console.log('封面正在动画中，跳过逐格翻滚动画');
                    return;
                }

                // 标记为动画中
                cover.userData.isAnimating = true;

                // 获取当前位置信息
                const startRow = cover.userData.row;
                const startCol = cover.userData.col;

                // 计算翻滚路径（向下翻滚直到出窗口）
                const rollPath = [];

                // 从当前位置开始，一直翻滚到窗口底部之外
                for (let i = 1; i <= this.gridRows - startRow + 2; i++) { // +2确保滚出窗口
                    rollPath.push({
                        row: startRow + i,
                        col: startCol,
                        index: (startRow + i) * this.gridCols + startCol
                    });
                }

                // console.log(`开始逐格翻滚: 从(${startRow},${startCol})翻滚${rollPath.length}格直到出窗口`);

                // 在起始位置创建新封面
                const newAlbum = this.getRandomAlbum();
                const textureLoader = new THREE.TextureLoader();
                const newTexture = textureLoader.load(newAlbum, (loadedTexture) => {
                    this.adjustTextureForCover(loadedTexture);
                });

                const newGeometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);
                const newMaterial = new THREE.MeshBasicMaterial({
                    map: newTexture,
                    transparent: true,
                    side: THREE.DoubleSide
                });

                const newCover = new THREE.Mesh(newGeometry, newMaterial);
                newCover.position.set(cover.position.x, cover.position.y, -0.01);
                newCover.userData = {
                    row: startRow,
                    col: startCol,
                    index: startRow * this.gridCols + startCol,
                    albumPath: newAlbum,
                    originalPosition: { ...cover.userData.originalPosition },
                    originalRotation: { ...cover.userData.originalRotation }
                };
                this.scene.add(newCover);

                // 提升翻滚封面的层级
                cover.position.z = 0.2;

                // 执行逐格翻滚动画
                this.executeGridRolling(cover, rollPath, newCover, startRow, startCol);
            }

            executeGridRolling(cover, rollPath, newCover, startRow, startCol) {
                let currentStep = 0;
                const stepDuration = 350; // 每格翻滚的持续时间

                // 当前位置
                let currentX = cover.position.x;
                let currentY = cover.position.y;

                const rollNextStep = () => {
                    if (currentStep >= rollPath.length) {
                        // 翻滚完成，移除翻滚封面
                        this.finishGridRolling(cover, newCover, startRow, startCol);
                        return;
                    }

                    const targetStep = rollPath[currentStep];

                    // 计算目标位置（即使超出网格也继续计算）
                    const targetX = this.startX + targetStep.col * (this.coverSize + this.gap) + this.coverSize / 2;
                    const targetY = this.startY - targetStep.row * (this.coverSize + this.gap) - this.coverSize / 2;

                    // 执行单格翻滚动画
                    this.animateGridRollStep(cover, currentX, currentY, targetX, targetY, stepDuration, () => {
                        // 更新当前位置
                        currentX = targetX;
                        currentY = targetY;
                        currentStep++;

                        // 短暂停顿后继续下一步
                        setTimeout(rollNextStep, 80);
                    });
                };

                // 开始翻滚
                rollNextStep();
            }

            animateGridRollStep(cover, startX, startY, targetX, targetY, duration, onComplete) {
                const startTime = Date.now();

                // 计算翻滚方向（这里只考虑向下翻滚）
                const isMovingDown = targetY < startY;

                if (!isMovingDown) {
                    // 如果不是向下移动，直接完成
                    onComplete();
                    return;
                }

                // 翻滚动画：沿着X轴旋转（前后翻滚）
                const rollDistance = this.coverSize; // 翻滚距离等于封面大小
                const startRotationX = cover.rotation.x; // 从当前角度开始，保持累计旋转

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // 使用缓动函数让翻滚更自然
                    const easeProgress = progress < 0.5
                        ? 2 * progress * progress
                        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

                    // 计算翻滚中心点（两个位置的中点）
                    const centerX = (startX + targetX) / 2;
                    const centerY = (startY + targetY) / 2;

                    // 翻滚角度（本步累加到 180°）
                    const rollAngle = easeProgress * Math.PI;
                    const currentAngle = startRotationX + rollAngle;

                    // 计算翻滚轨迹上的位置
                    // 使用圆弧轨迹模拟翻滚
                    const radius = rollDistance / 2;
                    const arcX = centerX;
                    const arcY = centerY + radius * Math.cos(rollAngle);
                    const arcZ = 0.2 + radius * Math.sin(rollAngle); // Z轴高度变化

                    // 应用位置
                    cover.position.x = arcX;
                    cover.position.y = arcY;
                    cover.position.z = Math.max(arcZ, 0.1); // 确保始终在其他封面之上

                    // 应用旋转（沿X轴翻滚，累计）
                    cover.rotation.x = currentAngle;
                    cover.rotation.y = 0;
                    cover.rotation.z = 0;

                    // 轻微的缩放效果
                    const scale = 1 + Math.sin(rollAngle) * 0.05;
                    cover.scale.set(scale, scale, scale);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 确保最终状态准确（保持累计角度，不归零）
                        cover.position.x = targetX;
                        cover.position.y = targetY;
                        cover.position.z = 0.2;
                        cover.rotation.x = startRotationX + Math.PI;
                        cover.rotation.y = 0;
                        cover.rotation.z = 0;
                        cover.scale.set(1, 1, 1);

                        onComplete();
                    }
                };

                animate();
            }

            finishGridRolling(cover, newCover, startRow, startCol) {
                // console.log(`翻滚完成: 封面已滚出窗口`);

                // 移除翻滚封面（已经滚出窗口）
                this.scene.remove(cover);

                // 将新封面提到正常层级并替换原来的封面
                newCover.position.z = 0;
                newCover.userData.isAnimating = false;

                // 在covers数组中用新封面替换原来的封面
                const originalIndex = this.covers.indexOf(cover);
                if (originalIndex !== -1) {
                    this.covers[originalIndex] = newCover;
                } else {
                    // 如果没找到原封面，直接添加新封面
                    this.covers.push(newCover);
                }

                // console.log(`起始位置(${startRow},${startCol})已放置新封面`);
            }
            
            playRowRollDropAnimation() {
                // 选择上半部分的随机行
                const upperHalfRows = Math.floor(this.gridRows / 2);
                const selectedRow = Math.floor(Math.random() * upperHalfRows);
                
                // console.log(`执行行翻滚动画：选择第 ${selectedRow} 行（从0开始）`);
                
                // 获取该行的所有封面
                const rowCovers = this.covers.filter(cover => cover.userData.row === selectedRow);
                
                // 按列排序，从左到右
                rowCovers.sort((a, b) => a.userData.col - b.userData.col);
                
                // 依次触发每个封面的rollDrop动画，间隔200-400ms随机
                rowCovers.forEach((cover, index) => {
                    // 为每个封面生成一个200-400之间的随机延迟
                    const randomDelay = Math.floor(Math.random() * 201) + 200; // 200-400之间的随机数
                    setTimeout(() => {
                        // 检查封面是否可以进行动画
                        if (!cover.userData.isAnimating) {
                            this.playRollDropAnimation(cover);
                        }
                    }, index * randomDelay);
                });
            }
            
            playRowDropAnimation() {
                // 选择上半部分的随机行
                const upperHalfRows = Math.floor(this.gridRows / 2);
                const selectedRow = Math.floor(Math.random() * upperHalfRows);
                
                // console.log(`执行行掉落动画：选择第 ${selectedRow} 行（从0开始）`);
                
                // 获取该行的所有封面
                const rowCovers = this.covers.filter(cover => cover.userData.row === selectedRow);
                
                // 按列排序，从左到右
                rowCovers.sort((a, b) => a.userData.col - b.userData.col);
                
                // 依次触发每个封面的drop动画，间隔200-400ms随机
                rowCovers.forEach((cover, index) => {
                    // 为每个封面生成一个200-400之间的随机延迟
                    const randomDelay = Math.floor(Math.random() * 201) + 200; // 200-400之间的随机数
                    setTimeout(() => {
                        // 检查封面是否可以进行动画
                        if (!cover.userData.isAnimating) {
                            this.playDropAnimation(cover);
                        }
                    }, index * randomDelay);
                });
            }

            playPinRotationAnimation(cover, chainLevel = 0) {
                // 检查封面是否正在进行其他动画
                if (cover.userData.isAnimating) {
                    // console.log('封面正在动画中，跳过图钉旋转动画');
                    return;
                }

                // 获取当前封面的位置
                const currentRow = cover.userData.row;
                const currentCol = cover.userData.col;

                // 寻找合适的2x2区域和旋转方向
                const targetPositions = this.findPinRotationTarget(currentRow, currentCol);

                if (!targetPositions) {
                    // console.log('未找到合适的2x2区域进行图钉旋转，执行普通翻转动画');
                    this.playFlipAnimation(cover);
                    return;
                }

                // 获取当前位置和目标位置
                const current = targetPositions.current;
                const target = targetPositions.target;
                const currentPos = targetPositions[current];
                const targetPos = targetPositions[target];

                // 如果是联动动画，添加联动标记
                const chainText = chainLevel > 0 ? `[联动${chainLevel}] ` : '';
                // console.log(`${chainText}开始图钉旋转动画: 从位置${current.substring(3)}(${currentRow},${currentCol})旋转到位置${target.substring(3)}(${targetPos.row},${targetPos.col})`);

                // 执行图钉旋转动画，传递chainLevel参数
                this.executePinRotation(cover, targetPositions, null, chainLevel);
            }

            findPinRotationTarget(currentRow, currentCol) {
                // 只支持对角线转换动画
                // 2x2网格布局：
                // 1 2
                // 3 4

                // 只保留4种对角线旋转配置
                const configurations = [
                    // 1. 当前位置为1（左上角），旋转到4（右下角）
                    {
                        current: 'pos1',
                        target: 'pos4',
                        positions: {
                            pos1: { row: currentRow, col: currentCol },       // 左上角（当前位置）
                            pos2: { row: currentRow, col: currentCol + 1 },   // 右上角
                            pos3: { row: currentRow + 1, col: currentCol },   // 左下角
                            pos4: { row: currentRow + 1, col: currentCol + 1 } // 右下角（目标位置）
                        }
                    },
                    // 2. 当前位置为2（右上角），旋转到3（左下角）
                    {
                        current: 'pos2',
                        target: 'pos3',
                        positions: {
                            pos1: { row: currentRow, col: currentCol - 1 },   // 左上角
                            pos2: { row: currentRow, col: currentCol },       // 右上角（当前位置）
                            pos3: { row: currentRow + 1, col: currentCol - 1 }, // 左下角（目标位置）
                            pos4: { row: currentRow + 1, col: currentCol }    // 右下角
                        }
                    },
                    // 3. 当前位置为3（左下角），旋转到2（右上角）
                    {
                        current: 'pos3',
                        target: 'pos2',
                        positions: {
                            pos1: { row: currentRow - 1, col: currentCol },   // 左上角
                            pos2: { row: currentRow - 1, col: currentCol + 1 }, // 右上角（目标位置）
                            pos3: { row: currentRow, col: currentCol },       // 左下角（当前位置）
                            pos4: { row: currentRow, col: currentCol + 1 }    // 右下角
                        }
                    },
                    // 4. 当前位置为4（右下角），旋转到1（左上角）
                    {
                        current: 'pos4',
                        target: 'pos1',
                        positions: {
                            pos1: { row: currentRow - 1, col: currentCol - 1 }, // 左上角（目标位置）
                            pos2: { row: currentRow - 1, col: currentCol },   // 右上角
                            pos3: { row: currentRow, col: currentCol - 1 },   // 左下角
                            pos4: { row: currentRow, col: currentCol }        // 右下角（当前位置）
                        }
                    }
                ];

                // 随机选择一个配置
                const shuffledConfigs = [...configurations].sort(() => Math.random() - 0.5);
                
                // 尝试每一种配置，直到找到有效的
                for (const config of shuffledConfigs) {
                    const { positions, current, target } = config;
                    
                    // 检查所有位置是否在网格范围内
                    let validPositions = true;
                    for (const pos of Object.values(positions)) {
                        if (pos.row < 0 || pos.row >= this.gridRows ||
                            pos.col < 0 || pos.col >= this.gridCols) {
                            validPositions = false;
                            break;
                        }
                    }
                    
                    if (!validPositions) continue;
                    
                    // 检查当前位置是否匹配
                    if (positions[current].row !== currentRow || positions[current].col !== currentCol) {
                        continue;
                    }
                    
                    // 查找对应的封面对象
                    const covers = {
                        pos1: this.findCoverAtPosition(positions.pos1.row, positions.pos1.col),
                        pos2: this.findCoverAtPosition(positions.pos2.row, positions.pos2.col),
                        pos3: this.findCoverAtPosition(positions.pos3.row, positions.pos3.col),
                        pos4: this.findCoverAtPosition(positions.pos4.row, positions.pos4.col)
                    };
                    
                    // 检查是否所有位置都有封面且没有在动画中
                    let validCovers = true;
                    for (const [key, coverObj] of Object.entries(covers)) {
                        if (!coverObj || coverObj.userData.isAnimating) {
                            validCovers = false;
                            break;
                        }
                    }
                    
                    if (!validCovers) continue;
                    
                    // 返回有效的配置
                    return {
                        pos1: positions.pos1,
                        pos2: positions.pos2,
                        pos3: positions.pos3,
                        pos4: positions.pos4,
                        covers,
                        current,
                        target
                    };
                }
                
                return null; // 没有找到有效配置
            }

            findCoverAtPosition(row, col) {
                return this.covers.find(cover =>
                    cover.userData.row === row && cover.userData.col === col
                );
            }

            executePinRotation(rotatingCover, targetPositions, newCover = null, chainLevel = 0) {
                // 获取当前位置和目标位置
                const current = targetPositions.current;
                const target = targetPositions.target;
                
                // 标记相关封面为动画中
                rotatingCover.userData.isAnimating = true;
                targetPositions.covers[target].userData.isAnimating = true;

                // 设置层级关系，视觉上平整，逻辑上分层
                // 确定旋转路径上的封面，使其稍高一些，便于视觉效果
                // 确定中间位置的封面，使其稍高，便于在下方更换封面
                const middlePositions = this.determineMiddlePositions(current, target);
                
                // 设置中间位置封面的Z轴层级稍高
                for (const posKey of middlePositions) {
                    if (targetPositions.covers[posKey]) {
                        targetPositions.covers[posKey].position.z = 0.002; // 中间位置稍高
                        // console.log(`设置位置${posKey.substring(3)}的Z轴层级为0.002`);
                    }
                }

                // 计算图钉位置（四个封面的交汇中心点）
                // 找到2x2区域的左上角位置
                const cornerRow = Math.min(targetPositions.pos1.row, targetPositions.pos2.row, 
                                         targetPositions.pos3.row, targetPositions.pos4.row);
                const cornerCol = Math.min(targetPositions.pos1.col, targetPositions.pos2.col, 
                                         targetPositions.pos3.col, targetPositions.pos4.col);
                
                // 图钉位置是2x2区域的中心点
                const pinX = this.startX + (cornerCol + 0.5) * (this.coverSize + this.gap) + this.coverSize / 2;
                const pinY = this.startY - (cornerRow + 0.5) * (this.coverSize + this.gap) - this.coverSize / 2;

                // 计算旋转封面的中心点坐标
                const currentPosX = this.startX + targetPositions[current].col * (this.coverSize + this.gap) + this.coverSize / 2;
                const currentPosY = this.startY - targetPositions[current].row * (this.coverSize + this.gap) - this.coverSize / 2;
                
                // 计算目标位置的中心点坐标
                const targetPosX = this.startX + targetPositions[target].col * (this.coverSize + this.gap) + this.coverSize / 2;
                const targetPosY = this.startY - targetPositions[target].row * (this.coverSize + this.gap) - this.coverSize / 2;

                // 计算旋转半径（从当前位置中心到图钉的距离）
                const radius = Math.sqrt(Math.pow(currentPosX - pinX, 2) + Math.pow(currentPosY - pinY, 2));

                // 计算起始角度和目标角度
                const startAngle = Math.atan2(currentPosY - pinY, currentPosX - pinX);
                
                // 计算目标角度（基于目标位置）
                const endAngle = Math.atan2(targetPosY - pinY, targetPosX - pinX);
                
                // 确定旋转方向（顺时针或逆时针，选择较短的路径）
                let targetAngle = endAngle;
                const angleDiff1 = (endAngle - startAngle + 2 * Math.PI) % (2 * Math.PI); // 顺时针
                const angleDiff2 = (startAngle - endAngle + 2 * Math.PI) % (2 * Math.PI); // 逆时针
                
                // 选择较短的旋转路径
                if (angleDiff1 <= angleDiff2) {
                    // 顺时针旋转
                    targetAngle = startAngle + angleDiff1;
                } else {
                    // 逆时针旋转
                    targetAngle = startAngle - angleDiff2;
                }

                // 在当前位置后方立即创建新封面
                let createdNewCover = newCover;
                if (!createdNewCover) {
                    const newAlbum = this.getRandomAlbum();
                    const textureLoader = new THREE.TextureLoader();
                    const newTexture = textureLoader.load(newAlbum, (loadedTexture) => {
                        this.adjustTextureForCover(loadedTexture);
                    });

                    const newGeometry = new THREE.PlaneGeometry(this.coverSize, this.coverSize);
                    const newMaterial = new THREE.MeshBasicMaterial({
                        map: newTexture,
                        transparent: true,
                        side: THREE.DoubleSide
                    });

                    createdNewCover = new THREE.Mesh(newGeometry, newMaterial);
                    createdNewCover.position.set(currentPosX, currentPosY, -0.01); // 新封面在后方
                    createdNewCover.userData = {
                        row: targetPositions[current].row,
                        col: targetPositions[current].col,
                        index: targetPositions[current].row * this.gridCols + targetPositions[current].col,
                        albumPath: newAlbum,
                        originalPosition: { x: currentPosX, y: currentPosY, z: 0 },
                        originalRotation: { x: 0, y: 0, z: 0 }
                    };
                    this.scene.add(createdNewCover);
                }

                // 执行旋转动画，传递chainLevel参数
                this.animatePinRotation(rotatingCover, targetPositions, pinX, pinY, radius, startAngle, targetAngle, createdNewCover, middlePositions, chainLevel);
            }
            
            // 确定中间位置的封面，用于设置Z轴层级
            determineMiddlePositions(current, target) {
                // 根据对角线旋转方向确定中间位置
                const middlePositions = [];
                
                // 对角线旋转
                if ((current === 'pos1' && target === 'pos4') || (current === 'pos4' && target === 'pos1')) {
                    // 1->4或4->1，中间位置是2和3
                    middlePositions.push('pos2', 'pos3');
                } else if ((current === 'pos2' && target === 'pos3') || (current === 'pos3' && target === 'pos2')) {
                    // 2->3或3->2，中间位置是1和4
                    middlePositions.push('pos1', 'pos4');
                }
                
                return middlePositions;
            }

            animatePinRotation(rotatingCover, targetPositions, pinX, pinY, radius, startAngle, targetAngle, newCover, middlePositions, chainLevel = 0) {
                const duration = 800; // 动画持续时间800ms
                const startTime = Date.now();

                // 保存原始Z轴位置
                const originalZ = rotatingCover.position.z;

                // 创建中间替换封面（当旋转封面被其他封面覆盖时使用）
                let midReplacementCover = null;
                let hasCreatedMidReplacement = false;
                let angleOffset = 0; // 角度偏移量，用于修正换封面后的角度

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // 线性匀速动画
                    const easedProgress = progress;

                    // 计算当前角度
                    const currentAngle = startAngle + (targetAngle - startAngle) * easedProgress;

                    // 计算当前位置（封面中心点的位置）
                    const currentX = pinX + radius * Math.cos(currentAngle);
                    const currentY = pinY + radius * Math.sin(currentAngle);

                    // 计算封面的旋转角度，模拟以图钉为支点的旋转
                    const rotationAngle = currentAngle - startAngle;

                    // 动态调整Z轴层级，实现封面在下方更换的效果
                    // 旋转初始和结束阶段，封面在上层；中间阶段，封面在下层
                    let zPosition;
                    if (progress < 0.3 || progress > 0.7) {
                        // 初始和结束阶段，封面在上层
                        zPosition = 0.001;
                    } else {
                        // 中间阶段，封面在下层，低于中间位置的封面
                        zPosition = -0.001;
                    }

                    // 计算进度，中间换封面
                    const rotationProgress = Math.abs(currentAngle - startAngle) / Math.abs(targetAngle - startAngle);

                    // 中间换封面，当封面在下方时进行更换
                    if (!hasCreatedMidReplacement && rotationProgress > 0.4 && rotationProgress < 0.6) {
                        angleOffset = this.createMidReplacementCover(rotatingCover, targetPositions);
                        hasCreatedMidReplacement = true;
                        // console.log('在中间位置下方更换封面');
                    }

                    // 应用变换
                    rotatingCover.position.set(currentX, currentY, zPosition);
                    rotatingCover.rotation.z = rotationAngle + angleOffset; // 加上角度偏移量

                    // 轻微的缩放效果增强视觉冲击
                    const scale = 1 + Math.sin(easedProgress * Math.PI * 2) * 0.03;
                    rotatingCover.scale.set(scale, scale, scale);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 动画完成，进行最终处理，传递chainLevel参数
                        this.finishPinRotation(rotatingCover, targetPositions, newCover, chainLevel);
                    }
                };

                animate();
            }

            easeInOutCubic(t) {
                return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
            }

            easeInQuad(t) {
                // 二次方缓动：模拟重力加速度，慢启动然后加速
                return t * t;
            }

            createMidReplacementCover(rotatingCover, targetPositions) {
                // 获取当前位置和目标位置
                const current = targetPositions.current;
                const target = targetPositions.target;
                
                // 当旋转封面经过中间位置时，在下方更换封面
                // console.log(`在对角线旋转过程中的中间位置下方更换封面，从${current}到${target}`);

                // 确保旋转封面处于较低的Z轴层级，使其在其他封面下方
                rotatingCover.position.z = -0.001;
                
                const newAlbum = this.getRandomAlbum();
                const textureLoader = new THREE.TextureLoader();
                const newTexture = textureLoader.load(newAlbum, (loadedTexture) => {
                    this.adjustTextureForCover(loadedTexture);

                    // 加载完成后立即更换旋转封面的材质
                    rotatingCover.material.map = newTexture;
                    rotatingCover.material.needsUpdate = true;
                    rotatingCover.userData.albumPath = newAlbum;

                    // console.log('旋转封面已在中间位置下方更换为新专辑');
                });

                // 对角线旋转（1->4或4->1，2->3或3->2）需要180度偏移
                const angleOffset = -Math.PI; // 180度偏移
                
                return angleOffset;
            }

            finishPinRotation(rotatingCover, targetPositions, newCover, chainLevel = 0) {
                // 获取当前位置和目标位置
                const current = targetPositions.current;
                const target = targetPositions.target;
                
                // console.log(`图钉旋转动画完成，从位置${current.substring(3)}旋转到位置${target.substring(3)}`);

                // 计算目标位置的精确坐标
                const targetPosX = this.startX + targetPositions[target].col * (this.coverSize + this.gap) + this.coverSize / 2;
                const targetPosY = this.startY - targetPositions[target].row * (this.coverSize + this.gap) - this.coverSize / 2;

                // 将旋转封面移动到目标位置，恢复正常Z轴层级
                rotatingCover.position.set(targetPosX, targetPosY, 0);
                rotatingCover.scale.set(1, 1, 1); // 重置缩放

                // 更新旋转封面的用户数据
                rotatingCover.userData.row = targetPositions[target].row;
                rotatingCover.userData.col = targetPositions[target].col;
                rotatingCover.userData.index = targetPositions[target].row * this.gridCols + targetPositions[target].col;
                rotatingCover.userData.originalPosition = { x: targetPosX, y: targetPosY, z: 0 };

                // 移除原目标位置的封面
                this.scene.remove(targetPositions.covers[target]);

                // 将新封面提到正常层级
                newCover.position.z = 0;

                // 更新covers数组
                const currentCoverIndex = this.covers.indexOf(targetPositions.covers[current]);
                const targetCoverIndex = this.covers.indexOf(targetPositions.covers[target]);

                // 用新封面替换当前位置的原封面
                if (currentCoverIndex !== -1) {
                    this.covers[currentCoverIndex] = newCover;
                }

                // 用旋转封面替换目标位置的原封面
                if (targetCoverIndex !== -1) {
                    this.covers[targetCoverIndex] = rotatingCover;
                }

                // 重置所有封面的z位置到正常层级
                for (const posKey of ['pos1', 'pos2', 'pos3', 'pos4']) {
                    if (targetPositions.covers[posKey] && posKey !== current && posKey !== target) {
                        targetPositions.covers[posKey].position.z = 0;
                        // console.log(`重置位置${posKey.substring(3)}的Z轴层级为0`);
                    }
                }

                // 重置动画状态
                rotatingCover.userData.isAnimating = false;
                newCover.userData.isAnimating = false;

                // console.log(`图钉旋转完成: 封面从位置${current.substring(3)}旋转到位置${target.substring(3)}，位置${current.substring(3)}已放置新封面，原位置${target.substring(3)}的封面已被移除`);
                
                // 添加联动动画逻辑
                // 限制联动层级，避免无限联动
                const maxChainLevel = 5; // 最大联动次数
                if (chainLevel < maxChainLevel) {
                    // 几乎无延迟地检查联动条件，使动画更加流畅
                    setTimeout(() => {
                        this.checkAndTriggerChainedAnimations(rotatingCover, targetPositions, chainLevel);
                    }, 1); // 1ms延迟，几乎无间隔
                }
            }
            
            // 检查并触发联动动画
            checkAndTriggerChainedAnimations(rotatingCover, lastTargetPositions, chainLevel) {
                // 获取上一次动画的目标位置
                const lastTarget = lastTargetPositions.target;
                const targetRow = rotatingCover.userData.row;
                const targetCol = rotatingCover.userData.col;
                
                // 随机决定是否触发联动（增加随机性）
                const shouldTriggerChain = Math.random() < 0.8; // 80%的概率触发联动
                
                if (!shouldTriggerChain) {
                    // console.log('随机决定不触发联动动画');
                    return;
                }
                
                // 随机选择联动类型
                const chainTypes = this.determineAvailableChainTypes(rotatingCover, lastTarget, chainLevel);
                
                if (chainTypes.length === 0) {
                    console.log('没有可用的联动动画类型');
                    return;
                }
                
                // 随机选择一种联动类型
                const randomChainType = chainTypes[Math.floor(Math.random() * chainTypes.length)];
                // console.log(`选择联动类型: ${randomChainType}`);
                
                // 根据联动类型触发相应的动画
                switch (randomChainType) {
                    case 'sequential':
                        this.triggerSequentialChain(rotatingCover, lastTarget, chainLevel);
                        break;
                    case 'downward':
                        this.triggerDownwardChain(rotatingCover, lastTarget, chainLevel);
                        break;
                    case 'fallAnimation':
                        this.triggerFallAnimation(rotatingCover, lastTarget, chainLevel);
                        break;
                }
            }
            
            // 确定可用的联动类型
            determineAvailableChainTypes(rotatingCover, lastTarget, chainLevel) {
                const availableTypes = [];
                const targetRow = rotatingCover.userData.row;
                const targetCol = rotatingCover.userData.col;
                
                // 检查是否可以进行顺势联动（同一2x2区域内的对角旋转）
                if (this.canTriggerSequentialChain(rotatingCover, lastTarget)) {
                    availableTypes.push('sequential');
                }
                
                // 检查是否可以进行向下联动（当前位置下方的封面旋转）
                if (this.canTriggerDownwardChain(rotatingCover)) {
                    availableTypes.push('downward');
                }
                
                // 检查是否可以触发飘落动画
                if (this.canTriggerFallAnimation(rotatingCover, lastTarget)) {
                    availableTypes.push('fallAnimation');
                }
                
                return availableTypes;
            }
            
            // 检查是否可以进行顺势联动
            canTriggerSequentialChain(rotatingCover, lastTarget) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 根据lastTarget和当前位置，确定可能的顺势联动封面
                let potentialCoverRow, potentialCoverCol;
                
                // 根据上一次旋转的目标位置确定潜在的顺势联动封面位置
                if (lastTarget === 'pos4') { // 如果旋转到了右下角
                    potentialCoverRow = row;
                    potentialCoverCol = col - 1; // 左侧封面
                } else if (lastTarget === 'pos3') { // 如果旋转到了左下角
                    potentialCoverRow = row;
                    potentialCoverCol = col + 1; // 右侧封面
                } else if (lastTarget === 'pos2') { // 如果旋转到了右上角
                    potentialCoverRow = row + 1;
                    potentialCoverCol = col; // 下方封面
                } else if (lastTarget === 'pos1') { // 如果旋转到了左上角
                    potentialCoverRow = row + 1;
                    potentialCoverCol = col; // 下方封面
                }
                
                // 检查潜在封面是否存在且未在动画中
                const potentialCover = this.findCoverAtPosition(potentialCoverRow, potentialCoverCol);
                return potentialCover && !potentialCover.userData.isAnimating;
            }
            
            // 检查是否可以进行向下联动
            canTriggerDownwardChain(rotatingCover) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 检查下方封面是否存在且未在动画中
                const downCover = this.findCoverAtPosition(row + 1, col);
                return downCover && !downCover.userData.isAnimating;
            }
            
            // 检查是否可以触发飘落动画
            canTriggerFallAnimation(rotatingCover, lastTarget) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 根据lastTarget和当前位置，确定可能触发飘落动画的封面
                let potentialCoverRow, potentialCoverCol;
                
                // 根据上一次旋转的目标位置确定潜在的飘落封面位置
                if (lastTarget === 'pos4' || lastTarget === 'pos3') { // 如果旋转到了下方
                    potentialCoverRow = row - 1;
                    potentialCoverCol = col; // 上方封面
                } else if (lastTarget === 'pos2' || lastTarget === 'pos1') { // 如果旋转到了上方
                    potentialCoverRow = row;
                    potentialCoverCol = col + (lastTarget === 'pos2' ? -1 : 1); // 左侧或右侧封面
                }
                
                // 检查潜在封面是否存在且未在动画中
                const potentialCover = this.findCoverAtPosition(potentialCoverRow, potentialCoverCol);
                return potentialCover && !potentialCover.userData.isAnimating;
            }
            
            // 触发顺势联动
            triggerSequentialChain(rotatingCover, lastTarget, chainLevel) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 根据lastTarget和当前位置，确定顺势联动封面
                let chainCoverRow, chainCoverCol;
                
                // 根据上一次旋转的目标位置确定联动封面位置
                if (lastTarget === 'pos4') { // 如果旋转到了右下角
                    chainCoverRow = row;
                    chainCoverCol = col - 1; // 左侧封面
                } else if (lastTarget === 'pos3') { // 如果旋转到了左下角
                    chainCoverRow = row;
                    chainCoverCol = col + 1; // 右侧封面
                } else if (lastTarget === 'pos2') { // 如果旋转到了右上角
                    chainCoverRow = row + 1;
                    chainCoverCol = col; // 下方封面
                } else if (lastTarget === 'pos1') { // 如果旋转到了左上角
                    chainCoverRow = row + 1;
                    chainCoverCol = col; // 下方封面
                }
                
                const chainCover = this.findCoverAtPosition(chainCoverRow, chainCoverCol);
                if (chainCover && !chainCover.userData.isAnimating) {
                    // console.log(`触发顺势联动: 位置(${chainCoverRow},${chainCoverCol})`);
                    setTimeout(() => {
                        this.playPinRotationAnimation(chainCover, chainLevel + 1);
                    }, 0); // 无延迟，立即触发
                }
            }
            
            // 触发向下联动
            triggerDownwardChain(rotatingCover, lastTarget, chainLevel) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 检查下方封面
                const downCover = this.findCoverAtPosition(row + 1, col);
                if (downCover && !downCover.userData.isAnimating) {
                    // console.log(`触发向下联动: 位置(${row+1},${col})`);
                    setTimeout(() => {
                        this.playPinRotationAnimation(downCover, chainLevel + 1);
                    }, 0); // 无延迟，立即触发
                }
            }
            
            // 触发飘落动画
            triggerFallAnimation(rotatingCover, lastTarget, chainLevel) {
                // 获取旋转封面当前位置
                const row = rotatingCover.userData.row;
                const col = rotatingCover.userData.col;
                
                // 根据lastTarget和当前位置，确定可能触发飘落动画的封面
                let fallCoverRow, fallCoverCol;
                
                // 根据上一次旋转的目标位置确定飘落封面位置
                if (lastTarget === 'pos4' || lastTarget === 'pos3') { // 如果旋转到了下方
                    fallCoverRow = row - 1;
                    fallCoverCol = col; // 上方封面
                } else if (lastTarget === 'pos2' || lastTarget === 'pos1') { // 如果旋转到了上方
                    fallCoverRow = row;
                    fallCoverCol = col + (lastTarget === 'pos2' ? -1 : 1); // 左侧或右侧封面
                }
                
                const fallCover = this.findCoverAtPosition(fallCoverRow, fallCoverCol);
                if (fallCover && !fallCover.userData.isAnimating) {
                    // console.log(`触发飘落动画: 位置(${fallCoverRow},${fallCoverCol})`);
                    setTimeout(() => {
                        // 随机选择飘落或滚动下落动画
                        const fallAnimationType = Math.random() < 0.5 ? 'drop' : 'rollDrop';
                        this.playAnimation(fallCover, fallAnimationType);
                    }, 0); // 无延迟，立即触发
                }
            }

            fadeInCover(cover) {
                const duration = 800;
                const startTime = Date.now();

                // 设置初始透明度
                cover.material.opacity = 0;

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    cover.material.opacity = progress;

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 确保最终透明度为1
                        cover.material.opacity = 1;
                    }
                };

                animate();
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            bindEvents() {
                // 键盘事件
                document.addEventListener('keydown', (e) => {
                    switch (e.code) {
                        case 'Escape':
                            this.exitScreensaver();
                            break;
                        case 'Space':
                            e.preventDefault();
                            this.toggleAnimations();
                            break;
                        case 'KeyR':
                            this.refreshAllCovers();
                            break;
                        case 'KeyF':
                            this.toggleFullscreen();
                            break;
                        case 'KeyH':
                        case 'Slash':
                            if (e.shiftKey && e.code === 'Slash') { // ? key
                                this.showHelp();
                            } else if (e.code === 'KeyH') {
                                this.showHelp();
                            }
                            break;
                    }
                });

                // 鼠标点击事件
                this.renderer.domElement.addEventListener('click', (event) => {
                    // 如果启用了桌面模式，检测三连击
                    if (this.desktopMode) {
                        this.clickCount++;
                        
                        // 清除之前的定时器
                        if (this.clickTimer) {
                            clearTimeout(this.clickTimer);
                        }
                        
                        // 设置新的定时器
                        this.clickTimer = setTimeout(() => {
                            // 如果是三连击，则处理点击事件
                            if (this.clickCount === 3) {
                                this.showStatusMessage('三连击触发');
                                this.handleClick(event);
                            }
                            // 重置点击计数
                            this.clickCount = 0;
                        }, this.clickTimeout);
                    } else {
                        // 非桌面模式，直接处理点击
                        this.handleClick(event);
                    }
                });

                // 鼠标移动事件
                let mouseTimer;
                document.addEventListener('mousemove', () => {
                    document.body.style.cursor = 'default';
                    clearTimeout(mouseTimer);
                    mouseTimer = setTimeout(() => {
                        document.body.style.cursor = 'none';
                    }, 60000);
                });
                
                // 设置按钮事件
                this.settingsButton.addEventListener('click', () => {
                    this.toggleSettingsPanel();
                });
                
                // 设置面板外部点击关闭
                document.addEventListener('click', (e) => {
                    // 处理面板关闭逻辑 - 点击任何非设置面板和非设置按钮的区域都关闭面板
                    if (this.isPanelVisible && 
                        !this.settingsPanel.contains(e.target) && 
                        !this.settingsButton.contains(e.target)) {
                        this.hideSettingsPanel();
                    }
                });
                
                // 设置控件事件
                this.initSettingsControls();
            }

            handleClick(event) {
                // 射线投射检测点击的封面
                const mouse = new THREE.Vector2();
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

                const raycaster = new THREE.Raycaster();
                raycaster.setFromCamera(mouse, this.camera);

                const intersects = raycaster.intersectObjects(this.covers);

                if (intersects.length > 0) {
                    const clickedCover = intersects[0].object;
                    const albumPath = clickedCover.userData.albumPath;

                    // 查找对应的URL
                    const url = this.albumUrls[albumPath];

                    if (url) {
                        // 获取MusicBrainz ID
                        const mbid = this.extractMusicBrainzId(url);
                        if (mbid) {
                            // 获取并显示外部链接，传递专辑路径
                            this.openExternalLinks(mbid, albumPath);
                        } else {
                            // 如果无法提取MBID，则只打开原始URL
                            window.open(url, '_blank');
                            // 复制URL到剪贴板
                            this.copyToClipboard(url);
                        }
                        
                        // 显示点击反馈
                        this.showClickFeedback(clickedCover);
                    }
                }
            }
            
            // 从MusicBrainz URL中提取MBID
            extractMusicBrainzId(url) {
                // 例如：https://musicbrainz.org/release/59211ea4-ffd2-4ad9-9a4e-941d3148024a
                const match = url.match(/musicbrainz\.org\/release\/([a-f0-9-]+)/i);
                return match ? match[1] : null;
            }
            
            // 获取外部链接（Spotify和YouTube Music）
            openExternalLinks(mbid, albumPath = null) {
                // 检查缓存中是否已有该MBID的外部链接数据
                if (this.externalLinksCache.has(mbid)) {
                    const cachedData = this.externalLinksCache.get(mbid);
                    // 更新缓存数据中的专辑路径
                    if (albumPath) {
                        cachedData.albumPath = albumPath;
                    }
                    this.showLinksDialog(cachedData);
                    return;
                }
                
                // 节流控制 - 确保API调用间隔至少1秒
                const now = Date.now();
                const timeSinceLastCall = now - this.lastApiCallTime;
                
                if (timeSinceLastCall < 1000) {
                    // 如果距离上次API调用不足1秒，延迟执行
                    setTimeout(() => {
                        this.openExternalLinks(mbid);
                    }, 1000 - timeSinceLastCall);
                    return;
                }
                
                // 更新最后API调用时间
                this.lastApiCallTime = now;
                
                // 显示加载中状态
                this.showStatusMessage(this.texts.loadingLinks);
                
                // 使用MusicBrainz API获取外部链接
                const apiUrl = `https://musicbrainz.org/ws/2/release/${mbid}?inc=url-rels&fmt=json`;
                
                fetch(apiUrl, {
                    headers: {
                        // 设置用户代理以遵循MusicBrainz API使用规则
                        'User-Agent': 'MacScreensaver/1.0 (https://github.com/birdy/MacScreensaver)'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.relations) {
                        // 查找Spotify、YouTube Music、Apple Music、iTunes、Deezer、Tidal、163链接以及其他外部链接
                        let spotifyUrl = null;
                        let youtubeMusicUrl = null;
                        let appleMusicUrl = null;
                        let itunesUrl = null;
                        let deezerUrl = null;
                        let tidalUrl = null;
                        let netease163Url = null;
                        let musicBrainzUrl = null;
                        let listenBrainzUrl = null;
                        let otherLinks = [];
                        
                        // 获取专辑标题
                        const albumTitle = data.title || "专辑";
                        
                        // 设置MusicBrainz链接
                        musicBrainzUrl = `https://musicbrainz.org/release/${mbid}`;
                        
                        // 设置ListenBrainz专辑页面链接（基于MBID）
                        listenBrainzUrl = `https://listenbrainz.org/release/${mbid}/`;
                        
                        for (const relation of data.relations) {
                            if (relation.url) {
                                const url = relation.url.resource;
                                
                                // 检查是否是ListenBrainz链接
                                if (url.includes('listenbrainz.org')) {
                                    listenBrainzUrl = url; // 优先使用API返回的ListenBrainz链接
                                } else if (relation.type === 'streaming' || relation.type === 'free streaming' || relation.type === 'purchase for download') {
                                    if (url.includes('spotify.com')) {
                                        spotifyUrl = url;
                                    } else if (url.includes('music.youtube.com')) {
                                        youtubeMusicUrl = url;
                                    } else if (url.includes('music.apple.com')) {
                                        appleMusicUrl = url;
                                    } else if (url.includes('itunes.apple.com')) {
                                        itunesUrl = url;
                                    } else if (url.includes('deezer.com')) {
                                        deezerUrl = url;
                                    } else if (url.includes('tidal.com')) {
                                        tidalUrl = url;
                                    } else if (url.includes('music.163.com')) {
                                        netease163Url = url;
                                    } else {
                                        // 其他流媒体链接
                                        otherLinks.push({
                                            name: this.getServiceNameFromUrl(url),
                                            url: url
                                        });
                                    }
                                } else {
                                    // 其他类型的链接
                                    otherLinks.push({
                                        name: relation.type || this.getServiceNameFromUrl(url),
                                        url: url
                                    });
                                }
                            }
                        }
                        
                        // 缓存结果
                        const linkData = { 
                            albumTitle,
                            albumPath: albumPath, // 添加专辑路径
                            musicBrainzUrl, 
                            listenBrainzUrl,
                            spotifyUrl, 
                            youtubeMusicUrl,
                            appleMusicUrl,
                            itunesUrl,
                            deezerUrl,
                            tidalUrl,
                            netease163Url,
                            otherLinks
                        };
                        this.externalLinksCache.set(mbid, linkData);
                        
                        // 显示链接对话框
                        this.showLinksDialog(linkData);
                    }
                })
                .catch(error => {
                    console.error('获取外部链接失败:', error);
                    this.showStatusMessage(this.texts.noExternalLinks);
                });
            }
            
            // 从URL中获取服务名称
            getServiceNameFromUrl(url) {
                try {
                    const hostname = new URL(url).hostname;
                    
                    // 自定义服务名称映射
                    if (hostname === 'music.youtube.com') {
                        return 'Youtube Music';
                    }
                    if (hostname.includes('163')) {
                        return 'Netease Music';
                    }
                    
                    // 其他情况返回域名
                    return hostname;
                } catch (e) {
                    return url;
                }
            }
            
            // 显示链接对话框
            showLinksDialog(linkData) {
                const { albumTitle, musicBrainzUrl, listenBrainzUrl, spotifyUrl, youtubeMusicUrl, appleMusicUrl, itunesUrl, deezerUrl, tidalUrl, netease163Url, otherLinks } = linkData;
                
                // 移除可能已存在的对话框
                const existingDialog = document.getElementById('links-dialog');
                if (existingDialog) {
                    // 清理旧的3D展示台
                    this.cleanupShowcase();
                    existingDialog.remove();
                }
                
                // 创建对话框元素
                const dialog = document.createElement('div');
                dialog.id = 'links-dialog';
                dialog.className = 'links-dialog';
                
                // 创建对话框内容
                //   <h3>${this.texts.externalLinksFor} "${albumTitle}"</h3>
                let dialogContent = `
                    <div class="dialog-header">
                      
                        <h3>${albumTitle}</h3>
                        <button class="close-button">&times;</button>
                    </div>
                    <div class="album-showcase-container">
                        <canvas id="album-showcase-canvas"></canvas>
                    </div>
                    <div class="dialog-content">
                        <ul class="links-list">
                `;
                
                // 添加第一行双列布局：MusicBrainz和ListenBrainz
                if (musicBrainzUrl || listenBrainzUrl) {
                    dialogContent += `<div class="two-column-container">`;
                    
                    if (musicBrainzUrl) {
                        dialogContent += `
                            <li class="column-item icon-link">
                                <a href="#" class="link-url" data-url="${musicBrainzUrl}">
                                    <img src="https://static.metabrainz.org/MB/header-logo-1f7dc2a.svg" alt="MusicBrainz" class="link-icon" />
                                </a>
                            </li>
                        `;
                    }
                    
                    if (listenBrainzUrl) {
                        dialogContent += `
                            <li class="column-item icon-link">
                                <a href="#" class="link-url" data-url="${listenBrainzUrl}">
                                    <img src="https://listenbrainz.org/static/img/listenbrainz-logo.svg" alt="ListenBrainz" class="link-icon" />
                                </a>
                            </li>
                        `;
                    }
                    
                    dialogContent += `</div>`;
                }
                
                // 添加Spotify链接
                // 音乐平台显示顺序配置（可自定义排序）
                const platformOrder = [
                    { key: 'youtubeMusicUrl', name: 'YouTube Music', linkClass: 'youtube-music-link', iconClass: 'youtube-music-icon', iconUrl: 'https://music.youtube.com/img/favicon_144.png', iconImgClass: 'youtube-icon' },
                    { key: 'netease163Url', name: '163', linkClass: 'netease163-link', iconClass: 'netease163-icon', iconUrl: 'https://s1.music.126.net/style/favicon.ico?v20180823', iconImgClass: 'netease163-icon-img' },
                    { key: 'spotifyUrl', name: 'Spotify', linkClass: 'spotify-link', iconClass: 'spotify-icon', iconUrl: 'https://open.spotifycdn.com/cdn/images/favicon.0f31d2ea.ico', iconImgClass: 'spotify-icon-img' },
                    { key: 'appleMusicUrl', name: 'Apple Music', linkClass: 'apple-music-link', iconClass: 'apple-music-icon', iconUrl: 'https://music.apple.com/assets/favicon/favicon-180.png', iconImgClass: 'apple-icon' },
                    { key: 'itunesUrl', name: 'iTunes', linkClass: 'itunes-link', iconClass: 'itunes-icon', iconUrl: 'https://www.apple.com/v/itunes/home/<USER>/images/overview/itunes_logo__dwjkvx332d0m_large.png', iconImgClass: 'itunes-icon-img' },
                    { key: 'deezerUrl', name: 'Deezer', linkClass: 'deezer-link', iconClass: 'deezer-icon', iconUrl: 'https://cdn-files.dzcdn.net/cache/images/common/favicon/favicon-144x144.07ae81558433f1009494.png', iconImgClass: 'deezer-icon-img' },
                    { key: 'tidalUrl', name: 'Tidal', linkClass: 'tidal-link', iconClass: 'tidal-icon', iconUrl: 'data:image/svg+xml,%3csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%20239.5%20159.7\'%20id=\'tidal-diamond\'%3e%3cpath%20d=\'M159.67%2039.938l-39.88%2039.88-39.88-39.88%2039.88-39.88zM159.669%20119.756l-39.88%2039.88-39.88-39.88%2039.88-39.88zM79.809%2039.912l-39.88%2039.88-39.88-39.88%2039.88-39.88zM239.505%2039.93l-39.88%2039.88-39.88-39.88%2039.88-39.88z\'/%3e%3c/svg%3e', iconImgClass: 'tidal-icon-img' }
                ];
                
                // 按配置的顺序显示音乐平台
                const platformLinks = { youtubeMusicUrl, netease163Url, spotifyUrl, appleMusicUrl, itunesUrl, deezerUrl, tidalUrl };
                
                for (const platform of platformOrder) {
                    const url = platformLinks[platform.key];
                    if (url) {
                        dialogContent += `
                            <li class="${platform.linkClass}">
                                <a href="#" class="link-url ${platform.iconClass}" data-url="${url}">
                                    <img src="${platform.iconUrl}" alt="${platform.name}" class="link-icon ${platform.iconImgClass}" />
                                </a>
                            </li>
                        `;
                    }
                }
                
                // 添加其他链接
                if (otherLinks && otherLinks.length > 0) {
                    for (const link of otherLinks) {
                        dialogContent += `
                            <li>
                                <span class="link-service">${link.name}</span>
                                <a href="#" class="link-url" data-url="${link.url}">
                                    ${link.url}
                                </a>
                            </li>
                        `;
                    }
                }
                
                // 如果没有找到任何链接
                if (!musicBrainzUrl && !listenBrainzUrl && !spotifyUrl && !youtubeMusicUrl && !appleMusicUrl && !itunesUrl && !deezerUrl && !tidalUrl && !netease163Url && (!otherLinks || otherLinks.length === 0)) {
                    dialogContent += `
                        <li class="no-links">
                            ${this.texts.noLinksFound}
                        </li>
                    `;
                }
                
                dialogContent += `
                        </ul>
                    </div>
                `;
                
                // 设置对话框内容
                dialog.innerHTML = dialogContent;
                
                // 添加样式
                const style = document.createElement('style');
                style.textContent = `
                    .links-dialog {
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: rgba(20, 20, 20, 0.8);
                        backdrop-filter: blur(10px);
                        -webkit-backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 25px;
                        z-index: 1000;
                        color: #fff;
                        max-width: 80%;
                        width: 600px;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 0 30px rgba(0, 0, 0, 0.6);
                        scrollbar-width: thin;
                        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
                    }
                    
                    /* 自定义滚动条样式 - Webkit浏览器 */
                    .links-dialog::-webkit-scrollbar {
                        width: 8px;
                    }
                    
                    .links-dialog::-webkit-scrollbar-track {
                        background: transparent;
                        border-radius: 10px;
                    }
                    
                    .links-dialog::-webkit-scrollbar-thumb {
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                        transition: background 0.3s ease;
                    }
                    
                    .links-dialog::-webkit-scrollbar-thumb:hover {
                        background: rgba(255, 255, 255, 0.3);
                    }
                    .dialog-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                        padding-bottom: 15px;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    }
                    .dialog-header h3 {
                        margin: 0;
                        font-size: 20px;
                        font-weight: 500;
                        letter-spacing: 0.5px;
                        color: rgba(255, 255, 255, 0.95);
                    }
                    .close-button {
                        background: rgba(255, 255, 255, 0.1);
                        border: none;
                        color: #fff;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 5px 10px;
                        border-radius: 50%;
                        width: 32px;
                        height: 32px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s ease;
                    }
                    .close-button:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                    
                    /* 3D展示台容器样式 - 透明悬浮效果 */
                    .album-showcase-container {
                        width: 100%;
                        height: 300px;
                        position: relative;
                        background: transparent;
                        border-radius: 0;
                        margin-bottom: 20px;
                        overflow: hidden;
                        border: none;
                        box-shadow: none;
                    }
                    
                    #album-showcase-canvas {
                        width: 100% !important;
                        height: 100% !important;
                        display: block;
                        border-radius: 0;
                    }
                    .links-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                    }
                    .links-list li {
                        padding: 15px;
                        margin-bottom: 10px;
                        border-radius: 12px;
                        background-color: rgba(255, 255, 255, 0.05);
                        display: flex;
                        flex-direction: column;
                        transition: all 0.2s ease;
                        cursor: pointer;
                    }
                    .links-list li:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    }
                    .links-list li:last-child {
                        margin-bottom: 0;
                    }
                    
                    /* 双列布局样式 */
                    .two-column-container {
                        display: flex;
                        gap: 10px;
                        margin-bottom: 10px;
                        min-height: 80px;
                    }
                    .column-item {
                        flex: 1;
                        display: flex !important;
                        flex-direction: column !important;
                        padding: 15px;
                        border-radius: 12px;
                        background-color: rgba(255, 255, 255, 0.05);
                        transition: all 0.2s ease;
                        cursor: pointer;
                        list-style: none;
                        min-height: 80px;
                        align-items: center;
                        justify-content: center;
                    }
                    .column-item:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    }
                    
                    /* 图标链接样式 */
                    .icon-link {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    .icon-link .link-url {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    .link-icon {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.2s ease;
                    }
                    .icon-link:hover .link-icon {
                        filter: brightness(1.1);
                        transform: scale(1.05);
                    }
                    
                    /* YouTube Music 特殊动效 */
                    .youtube-music-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .youtube-music-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .youtube-icon {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: youtubePulse 2s ease-in-out infinite;
                    }
                    
                    /* YouTube Music 脉冲动效 */
                    @keyframes youtubePulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(255, 0, 0, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(255, 0, 0, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .youtube-music-link:hover .youtube-icon {
                        animation: youtubeHoverPulse 1s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 0, 0.8));
                    }
                    
                    @keyframes youtubeHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 0, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(255, 0, 0, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .youtube-music-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(255, 0, 0, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: youtubeGlow 3s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes youtubeGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* 网易云音乐163 特殊动效 - 仿照YouTube红色主题 */
                    .netease163-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .netease163-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .netease163-icon-img {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: netease163Pulse 2s ease-in-out infinite;
                    }
                    
                    /* 网易云音乐163 脉冲动效 */
                    @keyframes netease163Pulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(255, 0, 0, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(255, 0, 0, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .netease163-link:hover .netease163-icon-img {
                        animation: netease163HoverPulse 1s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 0, 0.8));
                    }
                    
                    @keyframes netease163HoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 0, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(255, 0, 0, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .netease163-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(255, 0, 0, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: netease163Glow 3s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes netease163Glow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* Spotify 特殊动效 */
                    .spotify-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .spotify-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .spotify-icon-img {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: spotifyPulse 2.5s ease-in-out infinite;
                    }
                    
                    /* Spotify 脉冲动效 */
                    @keyframes spotifyPulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(29, 185, 84, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(29, 185, 84, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .spotify-link:hover .spotify-icon-img {
                        animation: spotifyHoverPulse 1.2s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(29, 185, 84, 0.8));
                    }
                    
                    @keyframes spotifyHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(29, 185, 84, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(29, 185, 84, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .spotify-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(29, 185, 84, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: spotifyGlow 3.5s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes spotifyGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* Apple Music 特殊动效 */
                    .apple-music-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .apple-music-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .apple-icon {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: applePulse 3s ease-in-out infinite;
                    }
                    
                    /* Apple Music 简单脉冲动效 */
                    @keyframes applePulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(255, 45, 85, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(255, 45, 85, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .apple-music-link:hover .apple-icon {
                        animation: appleHoverPulse 1s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 45, 85, 0.8));
                    }
                    
                    @keyframes appleHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 45, 85, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(255, 45, 85, 1));
                        }
                    }
                    
                    /* Apple 简化背景光晕效果 */
                    .apple-music-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(255, 45, 85, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: appleGlow 3s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes appleGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* Deezer 特殊动效 */
                    .deezer-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .deezer-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .deezer-icon-img {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: deezerPulse 2.2s ease-in-out infinite;
                    }
                    
                    /* Deezer 脉冲动效 */
                    @keyframes deezerPulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(255, 92, 51, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(255, 92, 51, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .deezer-link:hover .deezer-icon-img {
                        animation: deezerHoverPulse 1.1s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 92, 51, 0.8));
                    }
                    
                    @keyframes deezerHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 92, 51, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(255, 92, 51, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .deezer-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(255, 92, 51, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: deezerGlow 3.2s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes deezerGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* Tidal 特殊动效 */
                    .tidal-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .tidal-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .tidal-icon-img {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: tidalPulse 2.8s ease-in-out infinite;
                    }
                    
                    /* Tidal 脉冲动效 */
                    @keyframes tidalPulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(0, 255, 255, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(0, 255, 255, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .tidal-link:hover .tidal-icon-img {
                        animation: tidalHoverPulse 1.3s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 255, 255, 0.8));
                    }
                    
                    @keyframes tidalHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 255, 255, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(0, 255, 255, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .tidal-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: tidalGlow 3.8s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes tidalGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    
                    /* iTunes 特殊动效 */
                    .itunes-link {
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .itunes-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                    }
                    
                    .itunes-icon-img {
                        width: 100%;
                        height: 50px;
                        max-width: 120px;
                        object-fit: contain;
                        object-position: center;
                        filter: brightness(0.9);
                        transition: all 0.3s ease;
                        animation: itunesPulse 2.6s ease-in-out infinite;
                    }
                    
                    /* iTunes 脉冲动效 */
                    @keyframes itunesPulse {
                        0%, 100% {
                            filter: brightness(0.9) drop-shadow(0 0 5px rgba(100, 149, 237, 0.3));
                            transform: scale(1);
                        }
                        50% {
                            filter: brightness(1.1) drop-shadow(0 0 15px rgba(100, 149, 237, 0.6));
                            transform: scale(1.02);
                        }
                    }
                    
                    /* 悬停时的加强效果 */
                    .itunes-link:hover .itunes-icon-img {
                        animation: itunesHoverPulse 1.2s ease-in-out infinite;
                        filter: brightness(1.2) drop-shadow(0 0 20px rgba(100, 149, 237, 0.8));
                    }
                    
                    @keyframes itunesHoverPulse {
                        0%, 100% {
                            transform: scale(1.05);
                            filter: brightness(1.2) drop-shadow(0 0 20px rgba(100, 149, 237, 0.8));
                        }
                        50% {
                            transform: scale(1.08);
                            filter: brightness(1.3) drop-shadow(0 0 25px rgba(100, 149, 237, 1));
                        }
                    }
                    
                    /* 背景光晕效果 */
                    .itunes-link::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 100%;
                        background: radial-gradient(circle, rgba(100, 149, 237, 0.1) 0%, transparent 70%);
                        transform: translate(-50%, -50%);
                        animation: itunesGlow 3.6s ease-in-out infinite;
                        z-index: -1;
                        border-radius: 12px;
                    }
                    
                    @keyframes itunesGlow {
                        0%, 100% {
                            opacity: 0.3;
                            transform: translate(-50%, -50%) scale(1);
                        }
                        50% {
                            opacity: 0.6;
                            transform: translate(-50%, -50%) scale(1.1);
                        }
                    }
                    .link-service {
                        font-weight: 600;
                        margin-bottom: 8px;
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 16px;
                    }
                    .link-url {
                        color: #64b5f6;
                        text-decoration: none;
                        word-break: break-all;
                        transition: all 0.2s ease;
                        padding: 8px 0;
                        font-size: 14px;
                    }
                    .link-url:hover {
                        color: #90caf9;
                    }
                    .no-links {
                        color: rgba(255, 255, 255, 0.6);
                        text-align: center;
                        padding: 30px 0;
                        font-style: italic;
                    }
                    .dialog-backdrop {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: rgba(0, 0, 0, 0.7);
                        backdrop-filter: blur(3px);
                        -webkit-backdrop-filter: blur(3px);
                        z-index: 999;
                        animation: fadeIn 0.3s ease;
                    }
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    .links-dialog {
                        animation: slideIn 0.3s ease;
                    }
                    @keyframes slideIn {
                        from { 
                            opacity: 0;
                            transform: translate(-50%, -48%);
                        }
                        to { 
                            opacity: 1;
                            transform: translate(-50%, -50%);
                        }
                    }
                `;
                
                // 创建背景遮罩
                const backdrop = document.createElement('div');
                backdrop.className = 'dialog-backdrop';
                
                // 将对话框和样式添加到文档
                document.body.appendChild(style);
                document.body.appendChild(backdrop);
                document.body.appendChild(dialog);
                
                // 初始化3D展示台（在DOM添加后）
                setTimeout(() => {
                    this.initializeShowcase(linkData.albumPath || linkData.albumTitle);
                }, 100);
                
                // 添加点击事件处理
                const closeButton = dialog.querySelector('.close-button');
                closeButton.addEventListener('click', () => {
                    this.cleanupShowcase();
                    dialog.remove();
                    backdrop.remove();
                    style.remove();
                });
                
                // 点击背景关闭对话框
                backdrop.addEventListener('click', () => {
                    this.cleanupShowcase();
                    dialog.remove();
                    backdrop.remove();
                    style.remove();
                });
                
                // 为整个列表项添加点击事件
                const listItems = dialog.querySelectorAll('.links-list li');
                listItems.forEach(item => {
                    // 跳过没有链接的项目（如"没有找到链接"的提示）
                    if (item.classList.contains('no-links')) return;
                    
                    const link = item.querySelector('.link-url');
                    const url = link ? link.getAttribute('data-url') : null;
                    
                    // 获取服务名称
                    let serviceName;
                    const serviceSpan = item.querySelector('.link-service');
                    if (serviceSpan) {
                        serviceName = serviceSpan.textContent;
                    } else {
                        // 对于图标链接，根据URL判断服务名称
                        if (url && url.includes('musicbrainz.org')) {
                            serviceName = 'MusicBrainz';
                        } else if (url && url.includes('listenbrainz.org')) {
                            serviceName = 'ListenBrainz';
                        } else {
                            serviceName = 'Unknown Service';
                        }
                    }
                    
                    // 添加点击效果
                    item.addEventListener('mousedown', () => {
                        item.style.transform = 'translateY(0)';
                        item.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.1)';
                    });
                    
                    item.addEventListener('mouseup', () => {
                        item.style.transform = '';
                        item.style.boxShadow = '';
                    });
                    
                    // 点击整个列表项
                    item.addEventListener('click', () => {
                        if (url) {
                            this.handleLinkClick(url, serviceName, linkData);
                        }
                    });
                    
                    // 保留链接的点击事件，但阻止冒泡以避免触发两次
                    if (link) {
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation(); // 阻止冒泡到列表项
                            
                            if (url) {
                                this.handleLinkClick(url, serviceName, linkData);
                            }
                        });
                    }
                });
                
                // 显示状态消息
                this.showStatusMessage(this.texts.externalLinksReady);
            }
            
            copyToClipboard(text) {
                // 使用现代剪贴板API
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(text)
                        .then(() => {
                            // 显示复制成功消息
                            this.showStatusMessage('链接已复制到剪贴板');
                        })
                        .catch(err => {
                            console.error('无法复制到剪贴板:', err);
                            // 回退到传统方法
                            this.fallbackCopyToClipboard(text);
                        });
                } else {
                    // 回退到传统方法
                    this.fallbackCopyToClipboard(text);
                }
            }
            
            fallbackCopyToClipboard(text) {
                // 创建一个临时输入框来复制文本
                const tempInput = document.createElement('input');
                tempInput.style.position = 'absolute';
                tempInput.style.left = '-1000px';
                tempInput.style.top = '-1000px';
                tempInput.value = text;
                document.body.appendChild(tempInput);
                
                // 选择并复制
                tempInput.select();
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(tempInput);
                
                // 显示复制成功消息
                this.showStatusMessage('链接已复制到剪贴板');
            }

            showClickFeedback(cover) {
                // 创建点击反馈效果
                const originalScale = { ...cover.scale };
                const duration = 300;
                const startTime = Date.now();

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const scale = 1 + Math.sin(progress * Math.PI) * 0.1;
                    cover.scale.set(scale, scale, scale);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        cover.scale.copy(originalScale);
                    }
                };

                animate();
            }

            toggleAnimations() {
                if (this.animationInterval) {
                    clearInterval(this.animationInterval);
                    this.animationInterval = null;
                    this.showStatusMessage('动画已暂停 (按空格键恢复)');
                } else {
                    this.startAnimations();
                    this.showStatusMessage('动画已恢复');
                }
            }

            refreshAllCovers() {
                this.covers.forEach((cover, index) => {
                    setTimeout(() => {
                        const newAlbum = this.getRandomAlbum();
                        const textureLoader = new THREE.TextureLoader();
                        const newTexture = textureLoader.load(newAlbum, (loadedTexture) => {
                            this.adjustTextureForCover(loadedTexture);
                        });
                        cover.material.map = newTexture;
                        cover.material.needsUpdate = true;
                        cover.userData.albumPath = newAlbum;

                        // 添加刷新动画效果
                        this.fadeInCover(cover);
                    }, index * 50);
                });

                this.showStatusMessage('所有封面已刷新');
            }

            toggleFullscreen() {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen().catch(err => {
                        // console.log(`无法进入全屏模式: ${err.message}`);
                    });
                } else {
                    document.exitFullscreen();
                }
            }

            // 添加设置面板相关方法
            toggleSettingsPanel() {
                if (this.isPanelVisible) {
                    this.hideSettingsPanel();
                } else {
                    this.showSettingsPanel();
                }
            }
            
            showSettingsPanel() {
                this.settingsPanel.style.display = 'block';
                this.isPanelVisible = true;
            }
            
            hideSettingsPanel() {
                this.settingsPanel.style.display = 'none';
                this.isPanelVisible = false;
                // 隐藏面板时保存设置
                this.saveSettings();
            }
            
            // 保存设置到本地存储
            saveSettings() {
                try {
                    const settings = {
                        gridRows: this.gridRows,
                        gap: this.gap,
                        animationIntervalTime: this.animationIntervalTime,
                        animationWeights: this.animationWeights,
                        backgroundColor: this.scene ? this.scene.background.getHexString() : '000000',
                        desktopMode: this.desktopMode || false,
                        iframeModeEnabled: this.iframeModeEnabled || false,
                        iframeMusicBrainzModeEnabled: this.iframeMusicBrainzModeEnabled || false,
                        iframeListenBrainzModeEnabled: this.iframeListenBrainzModeEnabled || false
                    };
                    
                    localStorage.setItem('mac_screensaver_settings', JSON.stringify(settings));
                    console.log('设置已保存到本地存储:', settings);
                    
                    // 显示保存成功提示
                    this.showStatusMessage(this.texts.settingsSaved);
                } catch (error) {
                    console.warn('保存设置失败:', error);
                    this.showStatusMessage(this.texts.settingsSaveFailed);
                }
            }
            
            // 从本地存储加载设置
            loadSettings() {
                try {
                    const savedSettings = localStorage.getItem('mac_screensaver_settings');
                    if (savedSettings) {
                        const settings = JSON.parse(savedSettings);
                        console.log('从本地存储读取到设置:', settings);
                        
                        // 应用保存的设置
                        if (settings.gridRows !== undefined) this.gridRows = settings.gridRows;
                        if (settings.gap !== undefined) this.gap = settings.gap;
                        if (settings.animationIntervalTime !== undefined) this.animationIntervalTime = settings.animationIntervalTime;
                        if (settings.desktopMode !== undefined) this.desktopMode = settings.desktopMode;
                        if (settings.iframeModeEnabled !== undefined) this.iframeModeEnabled = settings.iframeModeEnabled;
                        if (settings.iframeMusicBrainzModeEnabled !== undefined) this.iframeMusicBrainzModeEnabled = settings.iframeMusicBrainzModeEnabled;
                        if (settings.iframeListenBrainzModeEnabled !== undefined) this.iframeListenBrainzModeEnabled = settings.iframeListenBrainzModeEnabled;
                        
                        if (settings.animationWeights) {
                            // 合并权重设置，确保所有键都存在
                            this.animationWeights = {
                                ...this.animationWeights,
                                ...settings.animationWeights
                            };
                        }
                        
                        // 背景颜色会在场景初始化后应用
                        this.savedBackgroundColor = settings.backgroundColor ? '#' + settings.backgroundColor : '#000000';
                        
                        console.log('已应用设置:', {
                            gridRows: this.gridRows,
                            gap: this.gap,
                            animationIntervalTime: this.animationIntervalTime,
                            animationWeights: this.animationWeights,
                            backgroundColor: this.savedBackgroundColor
                        });
                    } else {
                        console.log('未找到保存的设置，使用默认值');
                    }
                } catch (error) {
                    console.warn('加载设置失败:', error);
                }
            }
            
            // 初始化设置控件
            initSettingsControls() {
                // 行数设置
                const rowsRange = document.getElementById('rows-range');
                const rowsValue = document.getElementById('rows-value');
                rowsRange.value = this.gridRows;
                rowsValue.textContent = this.gridRows;
                
                rowsRange.addEventListener('input', () => {
                    const value = parseInt(rowsRange.value);
                    rowsValue.textContent = value;
                    this.gridRows = value;
                    this.recalculateGrid();
                    // 实时保存设置
                    this.saveSettings();
                });
                
                // 间隙大小设置
                const gapRange = document.getElementById('gap-range');
                const gapValue = document.getElementById('gap-value');
                gapRange.value = this.gap;
                gapValue.textContent = this.gap;
                
                gapRange.addEventListener('input', () => {
                    const value = parseFloat(gapRange.value);
                    gapValue.textContent = value;
                    this.gap = value;
                    this.recalculateGrid();
                    // 实时保存设置
                    this.saveSettings();
                });
                
                // 动画间隔设置
                const animationIntervalRange = document.getElementById('animation-interval-range');
                const animationIntervalValue = document.getElementById('animation-interval-value');
                animationIntervalRange.value = this.animationIntervalTime;
                animationIntervalValue.textContent = this.animationIntervalTime;
                
                animationIntervalRange.addEventListener('input', () => {
                    const value = parseInt(animationIntervalRange.value);
                    animationIntervalValue.textContent = value;
                    this.animationIntervalTime = value;
                    this.startAnimations(); // 重启动画以应用新间隔
                    // 实时保存设置
                    this.saveSettings();
                });
                
                // 动画权重设置
                this.initWeightSlider('flip-weight', 'flip');
                this.initWeightSlider('drop-weight', 'drop');
                this.initWeightSlider('linked-drop-weight', 'linkedDrop');
                this.initWeightSlider('roll-drop-weight', 'rollDrop');
                this.initWeightSlider('pin-rotation-weight', 'pinRotation');
                this.initWeightSlider('row-roll-weight', 'rowRollDrop');
                this.initWeightSlider('row-drop-weight', 'rowDrop');
                
                // 背景颜色设置
                const backgroundColorPicker = document.getElementById('background-color');
                // 设置颜色选择器的初始值为保存的颜色
                if (this.savedBackgroundColor) {
                    backgroundColorPicker.value = this.savedBackgroundColor;
                }
                
                backgroundColorPicker.addEventListener('input', () => {
                    this.updateBackgroundColor(backgroundColorPicker.value);
                    // 实时保存设置
                    this.saveSettings();
                });
                
                // 桌面模式设置
                const desktopModeToggle = document.getElementById('desktop-mode-toggle');
                desktopModeToggle.checked = this.desktopMode || false;
                
                desktopModeToggle.addEventListener('change', () => {
                    this.desktopMode = desktopModeToggle.checked;
                    // 实时保存设置
                    this.saveSettings();
                    
                    // 显示状态消息
                    if (this.desktopMode) {
                        this.showStatusMessage(this.texts.desktopModeEnabled);
                    } else {
                        this.showStatusMessage(this.texts.desktopModeDisabled);
                    }
                });
                
                // iframe模式设置
                const iframeModeToggle = document.getElementById('iframe-mode-toggle');
                iframeModeToggle.checked = this.iframeModeEnabled || false;
                
                iframeModeToggle.addEventListener('change', () => {
                    this.iframeModeEnabled = iframeModeToggle.checked;
                    // 实时保存设置
                    this.saveSettings();
                    
                    // 显示状态消息
                    if (this.iframeModeEnabled) {
                        this.showStatusMessage('已启用iframe模式，音乐链接将在当前页面打开');
                    } else {
                        this.showStatusMessage('已禁用iframe模式，音乐链接将在新标签页打开');
                    }
                });
                
                // MusicBrainz iframe模式设置
                const iframeMusicBrainzToggle = document.getElementById('iframe-musicbrainz-toggle');
                iframeMusicBrainzToggle.checked = this.iframeMusicBrainzModeEnabled || false;
                
                iframeMusicBrainzToggle.addEventListener('change', () => {
                    this.iframeMusicBrainzModeEnabled = iframeMusicBrainzToggle.checked;
                    // 实时保存设置
                    this.saveSettings();
                    
                    // 显示状态消息
                    if (this.iframeMusicBrainzModeEnabled) {
                        this.showStatusMessage('已启用MusicBrainz iframe模式');
                    } else {
                        this.showStatusMessage('已禁用MusicBrainz iframe模式');
                    }
                });
                
                // ListenBrainz iframe模式设置
                const iframeListenBrainzToggle = document.getElementById('iframe-listenbrainz-toggle');
                iframeListenBrainzToggle.checked = this.iframeListenBrainzModeEnabled || false;
                
                iframeListenBrainzToggle.addEventListener('change', () => {
                    this.iframeListenBrainzModeEnabled = iframeListenBrainzToggle.checked;
                    // 实时保存设置
                    this.saveSettings();
                    
                    // 显示状态消息
                    if (this.iframeListenBrainzModeEnabled) {
                        this.showStatusMessage('已启用ListenBrainz iframe模式');
                    } else {
                        this.showStatusMessage('已禁用ListenBrainz iframe模式');
                    }
                });
                
                // 添加重置按钮到设置面板
                this.addResetButton();
            }
            
            // 初始化权重滑块
            initWeightSlider(sliderId, weightKey) {
                const slider = document.getElementById(sliderId);
                const valueDisplay = document.getElementById(`${sliderId}-value`);
                
                slider.value = this.animationWeights[weightKey];
                valueDisplay.textContent = this.animationWeights[weightKey];
                
                slider.addEventListener('input', () => {
                    const value = parseFloat(slider.value);
                    valueDisplay.textContent = value;
                    this.animationWeights[weightKey] = value;
                    // 实时保存设置
                    this.saveSettings();
                });
            }
            
            // 添加重置按钮
            addResetButton() {
                const resetContainer = document.getElementById('reset-container');
                
                // 创建重置按钮
                const resetButton = document.createElement('button');
                resetButton.textContent = this.texts.resetAllSettings;
                resetButton.style.padding = '8px 16px';
                resetButton.style.background = 'rgba(255, 59, 48, 0.8)';
                resetButton.style.color = 'white';
                resetButton.style.border = 'none';
                resetButton.style.borderRadius = '6px';
                resetButton.style.cursor = 'pointer';
                resetButton.style.fontSize = '14px';
                resetButton.style.fontWeight = '500';
                resetButton.style.transition = 'all 0.2s ease';
                
                // 鼠标悬停效果
                resetButton.addEventListener('mouseover', () => {
                    resetButton.style.background = 'rgba(255, 59, 48, 1)';
                });
                
                resetButton.addEventListener('mouseout', () => {
                    resetButton.style.background = 'rgba(255, 59, 48, 0.8)';
                });
                
                // 点击事件 - 重置所有设置
                resetButton.addEventListener('click', () => {
                    this.resetAllSettings();
                });
                
                // 添加到容器
                resetContainer.appendChild(resetButton);
                
                // 添加清除缓存按钮
                const clearCacheButton = document.createElement('button');
                clearCacheButton.textContent = this.texts.clearCache;
                clearCacheButton.style.padding = '8px 16px';
                clearCacheButton.style.background = 'rgba(120, 120, 128, 0.5)';
                clearCacheButton.style.color = 'white';
                clearCacheButton.style.border = 'none';
                clearCacheButton.style.borderRadius = '6px';
                clearCacheButton.style.cursor = 'pointer';
                clearCacheButton.style.fontSize = '14px';
                clearCacheButton.style.fontWeight = '500';
                clearCacheButton.style.transition = 'all 0.2s ease';
                clearCacheButton.style.marginLeft = '10px';
                
                // 鼠标悬停效果
                clearCacheButton.addEventListener('mouseover', () => {
                    clearCacheButton.style.background = 'rgba(120, 120, 128, 0.7)';
                });
                
                clearCacheButton.addEventListener('mouseout', () => {
                    clearCacheButton.style.background = 'rgba(120, 120, 128, 0.5)';
                });
                
                // 点击事件 - 清除缓存
                clearCacheButton.addEventListener('click', () => {
                    this.clearAllCache();
                });
                
                // 添加到容器
                resetContainer.appendChild(clearCacheButton);
            }
            
            // 重置所有设置到默认值
            resetAllSettings() {
                // 默认设置
                this.gridRows = 6;
                this.gap = 0;
                this.animationIntervalTime = 3000;
                this.animationWeights = {
                    flip: 15,
                    drop: 15,
                    linkedDrop: 15,
                    rollDrop: 15,
                    pinRotation: 15,
                    rowRollDrop: 12.5,
                    rowDrop: 12.5
                };
                
                // 重置桌面模式
                this.desktopMode = false;
                
                // 更新UI控件
                document.getElementById('rows-range').value = this.gridRows;
                document.getElementById('rows-value').textContent = this.gridRows;
                document.getElementById('desktop-mode-toggle').checked = false;
                
                document.getElementById('gap-range').value = this.gap;
                document.getElementById('gap-value').textContent = this.gap;
                
                document.getElementById('animation-interval-range').value = this.animationIntervalTime;
                document.getElementById('animation-interval-value').textContent = this.animationIntervalTime;
                
                document.getElementById('background-color').value = '#000000';
                
                // 更新权重滑块
                this.updateWeightSlider('flip-weight', 'flip');
                this.updateWeightSlider('drop-weight', 'drop');
                this.updateWeightSlider('linked-drop-weight', 'linkedDrop');
                this.updateWeightSlider('roll-drop-weight', 'rollDrop');
                this.updateWeightSlider('pin-rotation-weight', 'pinRotation');
                this.updateWeightSlider('row-roll-weight', 'rowRollDrop');
                this.updateWeightSlider('row-drop-weight', 'rowDrop');
                
                // 应用设置
                this.updateBackgroundColor('#000000');
                this.recalculateGrid();
                this.startAnimations();
                
                // 清除本地存储中的设置
                localStorage.removeItem('mac_screensaver_settings');
                
                this.showStatusMessage(this.texts.allSettingsReset);
            }
            
            // 清除所有缓存
            clearAllCache() {
                // 清除封面缓存
                localStorage.removeItem('musicbrainz_cover_cache');
                // 清除专辑列表缓存
                localStorage.removeItem('musicbrainz_albums_cache');
                // 清除设置缓存
                localStorage.removeItem('mac_screensaver_settings');
                
                this.showStatusMessage(this.texts.allCacheCleared);
                
                // 3秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            }
            
            // 更新权重滑块值
            updateWeightSlider(sliderId, weightKey) {
                const slider = document.getElementById(sliderId);
                const valueDisplay = document.getElementById(`${sliderId}-value`);
                
                slider.value = this.animationWeights[weightKey];
                valueDisplay.textContent = this.animationWeights[weightKey];
            }
            
            // 更新背景颜色
            updateBackgroundColor(color) {
                this.scene.background = new THREE.Color(color);
            }
            
            showHelp() {
                const helpDiv = document.createElement('div');
                helpDiv.className = 'help-overlay';
                helpDiv.style.display = 'flex';

                helpDiv.innerHTML = `
                    <div style="max-width: 600px; padding: 40px; background: rgba(20, 20, 20, 0.95); border-radius: 20px; text-align: left;">
                        <h3 style="margin: 0 0 20px 0; text-align: center; color: #fff;">Mac专辑封面屏保 - Three.js版本</h3>
                        <div style="margin-bottom: 15px;"><strong>鼠标操作：</strong></div>
                        <div style="margin-left: 20px; margin-bottom: 15px;">
                            • 点击封面：跳转到音乐平台搜索并复制链接到剪贴板<br>
                            • 右上角设置：调整网格、动画和外观<br>
                            • 桌面模式：启用后只响应三连击左键，适合作为桌面壁纸使用
                        </div>
                        <div style="margin-bottom: 15px;"><strong>键盘快捷键：</strong></div>
                        <div style="margin-left: 20px; margin-bottom: 15px;">
                            • <code>ESC</code> - 退出屏保<br>
                            • <code>空格</code> - 暂停/恢复动画<br>
                            • <code>R</code> - 刷新所有封面<br>
                            • <code>F</code> - 切换全屏模式<br>
                            • <code>H</code> 或 <code>?</code> - 显示此帮助
                        </div>
                        <div style="margin-bottom: 15px;"><strong>动画效果：</strong></div>
                        <div style="margin-left: 20px; margin-bottom: 20px;">
                            • 翻转替换：封面3D翻转显示新专辑<br>
                            • 掉落动画：封面3D掉落效果<br>
                            • 逐格翻滚：封面沿网格逐格向下翻滚<br>
                            • 移动动画：封面在3D空间中移动<br>
                            • 图钉旋转：封面绕图钉旋转到新位置
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button onclick="this.parentElement.parentElement.remove()"
                                    style="padding: 10px 20px; background: #007AFF; color: white; border: none; border-radius: 10px; cursor: pointer;">
                                关闭帮助
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(helpDiv);

                // 点击背景关闭
                helpDiv.addEventListener('click', (e) => {
                    if (e.target === helpDiv) {
                        helpDiv.remove();
                    }
                });
            }

            showStatusMessage(message) {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'status-message';
                statusDiv.textContent = message;
                document.body.appendChild(statusDiv);

                // 显示动画
                setTimeout(() => {
                    statusDiv.style.opacity = '1';
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    statusDiv.style.opacity = '0';
                    setTimeout(() => {
                        if (statusDiv.parentNode) {
                            statusDiv.parentNode.removeChild(statusDiv);
                        }
                    }, 300);
                }, 2000);
            }

            exitScreensaver() {
                // 可以添加退出逻辑，比如跳转到其他页面
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
                this.showStatusMessage(this.texts.helpEsc);
            }

            hideLoading() {
                this.loading.style.display = 'none';
            }

            showError(message) {
                // 隐藏加载动画
                const spinner = this.loading.querySelector('.loading-spinner');
                if (spinner) {
                    spinner.style.display = 'none';
                }

                // 显示错误消息
                if (this.loadingText) {
                    this.loadingText.innerHTML = `<div style="color: #ff6b6b;">${message}</div>`;
                }

                // 清空进度信息
                if (this.loadingProgress) {
                    this.loadingProgress.textContent = '';
                }
            }

            // ===== iframe播放器相关方法 =====
            
            // 初始化iframe播放器
            initIframePlayer() {
                try {
                    console.log('初始化iframe播放器...');
                    
                    // 初始化iframe 3D展示台
                    this.initIframeShowcase();
                    
                    // 绑定iframe控制按钮事件
                    this.bindIframeEvents();
                    
                    console.log('iframe播放器初始化完成');
                } catch (error) {
                    console.error('iframe播放器初始化失败:', error);
                }
            }
            
            // 初始化iframe 3D展示台
            initIframeShowcase() {
                try {
                    const canvas = this.iframeShowcaseCanvas;
                    if (!canvas) return;
                    
                    // 创建场景
                    this.iframeShowcaseScene = new THREE.Scene();
                    
                    // 创建相机
                    const aspect = 1; // 正方形画布
                    this.iframeShowcaseCamera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
                    this.iframeShowcaseCamera.position.set(0, 0, 2);
                    
                    // 创建渲染器
                    this.iframeShowcaseRenderer = new THREE.WebGLRenderer({
                        canvas: canvas,
                        alpha: true, // 启用透明度
                        antialias: true
                    });
                    this.iframeShowcaseRenderer.setSize(40, 40);
                    this.iframeShowcaseRenderer.setClearColor(0x000000, 0); // 完全透明背景
                    
                    // 添加灯光
                    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                    this.iframeShowcaseScene.add(ambientLight);
                    
                    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                    directionalLight.position.set(1, 1, 1);
                    this.iframeShowcaseScene.add(directionalLight);
                    
                    console.log('iframe 3D展示台初始化完成');
                } catch (error) {
                    console.error('iframe 3D展示台初始化失败:', error);
                }
            }
            
            // 初始化樱花飘落动画
            initCherryBlossomAnimation() {
                try {
                    // 先停止之前的动画
                    this.stopCherryBlossomAnimation();

                    // 等待jQuery加载完成后初始化樱花飘落动画
                    if (typeof $ !== 'undefined' && typeof RENDERER !== 'undefined') {
                        // 确保容器存在
                        const container = document.getElementById('jsi-cherry-container');
                        if (container) {
                            // 清空容器内容
                            container.innerHTML = '';
                            // 重新初始化
                            RENDERER.init();
                            console.log('樱花飘落动画初始化完成');
                        } else {
                            console.warn('樱花飘落动画容器未找到');
                        }
                    } else {
                        console.warn('jQuery或樱花飘落动画脚本未加载');
                        // 延迟重试
                        setTimeout(() => {
                            this.initCherryBlossomAnimation();
                        }, 100);
                    }
                } catch (error) {
                    console.error('樱花飘落动画初始化失败:', error);
                }
            }

            // 停止樱花飘落动画
            stopCherryBlossomAnimation() {
                try {
                    if (typeof RENDERER !== 'undefined') {
                        // 调用RENDERER的stop方法
                        RENDERER.stop();

                        // 清空容器
                        const container = document.getElementById('jsi-cherry-container');
                        if (container) {
                            container.innerHTML = '';
                        }
                        console.log('樱花飘落动画已停止');
                    }
                } catch (error) {
                    console.error('停止樱花飘落动画失败:', error);
                }
            }
            

            

            
            // 绑定iframe事件
            bindIframeEvents() {
                // 先清理可能存在的旧事件监听器
                this.unbindIframeEvents();

                // 固定按钮
                this.iframePinBtn.addEventListener('click', () => {
                    this.toggleIframePin();
                });

                // 展开/收缩按钮
                this.iframeExpandBtn.addEventListener('click', () => {
                    this.toggleIframeExpand();
                });

                // 关闭按钮
                this.iframeCloseBtn.addEventListener('click', () => {
                    this.closeIframePlayer();
                });

                // 鼠标进入/离开事件
                this.iframeMouseEnterHandler = () => {
                    this.onIframeMouseEnter();
                };
                this.iframeMouseLeaveHandler = () => {
                    this.onIframeMouseLeave();
                };

                this.iframePlayer.addEventListener('mouseenter', this.iframeMouseEnterHandler);
                this.iframePlayer.addEventListener('mouseleave', this.iframeMouseLeaveHandler);
            }

            // 解绑iframe事件
            unbindIframeEvents() {
                if (this.iframeMouseEnterHandler) {
                    this.iframePlayer.removeEventListener('mouseenter', this.iframeMouseEnterHandler);
                }
                if (this.iframeMouseLeaveHandler) {
                    this.iframePlayer.removeEventListener('mouseleave', this.iframeMouseLeaveHandler);
                }
            }
            
            // 打开iframe播放器
            openIframePlayer(url, albumData) {
                console.log('打开iframe播放器:', url, albumData);
                
                this.currentIframeUrl = url;
                this.currentAlbumData = albumData;
                
                // 更新标题和服务信息
                this.iframeTitle.textContent = albumData.albumTitle || '未知专辑';
                this.iframeService.textContent = this.getServiceNameFromUrl(url);
                
                // 显示加载状态
                this.iframeLoading.style.display = 'block';
                this.iframeEmbed.style.display = 'none';
                
                // 显示播放器
                this.iframePlayer.style.display = 'block';
                setTimeout(() => {
                    this.iframePlayer.classList.add('expanded');
                    this.isIframeExpanded = true;
                    this.iframeExpandBtn.textContent = '⬇️';

                    // 初始化樱花飘落动画
                    this.initCherryBlossomAnimation();
                }, 100);

                // 更新封面展示
                this.updateIframeShowcase(albumData);
                
                // 加载iframe内容
                this.loadIframeContent(url);
            }
            
            // 加载iframe内容
            loadIframeContent(url) {
                const iframe = this.iframeEmbed;
                
                // 转换URL为iframe兼容格式
                const embedUrl = this.convertToEmbedUrl(url);
                console.log('原始URL:', url);
                console.log('转换后的URL:', embedUrl);
                
                // 清理之前的事件监听器
                iframe.onload = null;
                iframe.onerror = null;
                
                // 设置加载超时
                const loadTimeout = setTimeout(() => {
                    console.warn('iframe加载超时');
                    this.iframeLoading.innerHTML = `
                        <div style="color: #ff9800;">
                            <div>加载超时</div>
                            <div style="font-size: 12px; margin-top: 5px;">
                                <a href="${url}" target="_blank" style="color: #4a9eff;">在新标签页中打开</a>
                            </div>
                        </div>
                    `;
                }, 10000); // 10秒超时
                
                iframe.onload = () => {
                    console.log('iframe内容加载完成');
                    clearTimeout(loadTimeout);
                    this.iframeLoading.style.display = 'none';
                    iframe.style.display = 'block';

                    // 重新绑定鼠标事件，确保iframe重新加载后鼠标监听正常工作
                    setTimeout(() => {
                        this.bindIframeEvents();
                        console.log('iframe播放器鼠标事件已重新绑定');
                    }, 100);
                };
                
                iframe.onerror = (error) => {
                    console.error('iframe内容加载失败:', error);
                    clearTimeout(loadTimeout);
                    this.iframeLoading.innerHTML = `
                        <div style="color: #ff6b6b;">
                            <div>加载失败</div>
                            <div style="font-size: 12px; margin-top: 5px;">
                                <a href="${url}" target="_blank" style="color: #4a9eff;">在新标签页中打开</a>
                            </div>
                        </div>
                    `;
                };
                
                // 先重置iframe
                iframe.src = 'about:blank';
                
                // 稍等一下再设置真正的URL
                setTimeout(() => {
                    iframe.src = embedUrl;
                    console.log('设置iframe src:', embedUrl);
                }, 100);
            }
            
            // 更新iframe封面展示
            updateIframeShowcase(albumData) {
                if (!this.iframeShowcaseScene || !this.iframeShowcaseCamera || !this.iframeShowcaseRenderer) return;
                
                // 清理旧的封面
                if (this.iframeShowcaseCover) {
                    this.iframeShowcaseScene.remove(this.iframeShowcaseCover);
                    if (this.iframeShowcaseCover.material.map) {
                        this.iframeShowcaseCover.material.map.dispose();
                    }
                    this.iframeShowcaseCover.material.dispose();
                    this.iframeShowcaseCover.geometry.dispose();
                }
                
                // 创建新的封面
                const textureLoader = new THREE.TextureLoader();
                const albumPath = albumData.albumPath || `bg/${albumData.albumTitle}.jpg`;
                
                textureLoader.load(albumPath, (texture) => {
                    // 创建圆形几何体而不是正方形，大小x2
                    const geometry = new THREE.CircleGeometry(1.0, 32); // 半径1.0（原来0.5的2倍），32个分段使圆形更光滑
                    const material = new THREE.MeshLambertMaterial({ map: texture });
                    
                    this.iframeShowcaseCover = new THREE.Mesh(geometry, material);
                    this.iframeShowcaseScene.add(this.iframeShowcaseCover);
                    
                    // 开始渲染循环
                    this.startIframeShowcaseAnimation();
                }, undefined, (error) => {
                    console.warn('加载iframe封面失败:', error);
                });
            }
            
            // 开始iframe展示台动画
            startIframeShowcaseAnimation() {
                if (this.iframeShowcaseAnimationId) {
                    cancelAnimationFrame(this.iframeShowcaseAnimationId);
                }
                
                const animate = () => {
                    this.iframeShowcaseAnimationId = requestAnimationFrame(animate);
                    
                    // 旋转封面 - 改为Z轴旋转（像黑胶唱片一样平面内旋转），向右旋转
                    if (this.iframeShowcaseCover) {
                        this.iframeShowcaseCover.rotation.z -= 0.01; // 负值表示向右旋转
                    }
                    

                    
                    // 渲染展示台
                    if (this.iframeShowcaseRenderer && this.iframeShowcaseScene && this.iframeShowcaseCamera) {
                        this.iframeShowcaseRenderer.render(this.iframeShowcaseScene, this.iframeShowcaseCamera);
                    }
                };
                
                animate();
            }
            
            // 停止iframe展示台动画
            stopIframeShowcaseAnimation() {
                if (this.iframeShowcaseAnimationId) {
                    cancelAnimationFrame(this.iframeShowcaseAnimationId);
                    this.iframeShowcaseAnimationId = null;
                }
            }
            
            // 切换iframe固定状态
            toggleIframePin() {
                this.isIframePinned = !this.isIframePinned;
                this.iframePinBtn.classList.toggle('active', this.isIframePinned);
                
                if (this.isIframePinned) {
                    this.iframePinBtn.textContent = '📍';
                    console.log('iframe已固定');
                } else {
                    this.iframePinBtn.textContent = '📌';
                    console.log('iframe已取消固定');
                }
            }
            
            // 切换iframe展开状态
            toggleIframeExpand() {
                if (this.isIframeExpanded) {
                    this.collapseIframePlayer();
                } else {
                    this.expandIframePlayer();
                }
            }
            
            // 展开iframe播放器
            expandIframePlayer() {
                this.iframePlayer.classList.remove('collapsed');
                this.iframePlayer.classList.add('expanded');
                this.isIframeExpanded = true;
                this.iframeExpandBtn.textContent = '⬇️';
                console.log('iframe播放器已展开');
            }
            
            // 收缩iframe播放器
            collapseIframePlayer() {
                this.iframePlayer.classList.remove('expanded');
                this.iframePlayer.classList.add('collapsed');
                this.isIframeExpanded = false;
                this.iframeExpandBtn.textContent = '⬆️';
                console.log('iframe播放器已收缩');
            }
            
            // 测试163链接功能 - 用于调试和演示
            test163Link() {
                console.log('测试163链接功能');
                const testLinkData = {
                    albumTitle: '测试专辑 - 网易云音乐163',
                    albumPath: 'test/163',
                    musicBrainzUrl: null,
                    listenBrainzUrl: null,
                    spotifyUrl: null,
                    youtubeMusicUrl: null,
                    appleMusicUrl: null,
                    itunesUrl: null,
                    deezerUrl: null,
                    tidalUrl: null,
                    netease163Url: 'https://music.163.com/outchain/player?type=1&id=73257118&auto=1&height=430',
                    otherLinks: []
                };
                
                this.showLinksDialog(testLinkData);
            }
            
            // 关闭iframe播放器
            closeIframePlayer() {
                console.log('关闭iframe播放器');

                // 停止动画
                this.stopIframeShowcaseAnimation();

                // 停止樱花飘落动画
                this.stopCherryBlossomAnimation();

                // 解绑事件监听器
                this.unbindIframeEvents();

                // 清理iframe内容
                this.iframeEmbed.src = '';

                // 隐藏播放器
                this.iframePlayer.classList.remove('expanded', 'collapsed');
                setTimeout(() => {
                    this.iframePlayer.style.display = 'none';
                }, 400);

                // 重置状态
                this.isIframeExpanded = false;
                this.isIframePinned = false;
                this.iframePinBtn.classList.remove('active');
                this.iframePinBtn.textContent = '📌';
                this.iframeExpandBtn.textContent = '⬇️';

                // 清理变量
                this.currentIframeUrl = null;
                this.currentAlbumData = null;
            }
            
            // 鼠标进入iframe
            onIframeMouseEnter() {
                if (this.iframeMouseLeaveTimer) {
                    clearTimeout(this.iframeMouseLeaveTimer);
                    this.iframeMouseLeaveTimer = null;
                }
                
                // 如果当前是收缩状态且未固定，则展开
                if (!this.isIframeExpanded && !this.isIframePinned) {
                    this.expandIframePlayer();
                }
            }
            
            // 鼠标离开iframe
            onIframeMouseLeave() {
                // 如果未固定，设置延迟收缩
                if (!this.isIframePinned) {
                    this.iframeMouseLeaveTimer = setTimeout(() => {
                        if (this.isIframeExpanded) {
                            this.collapseIframePlayer();
                        }
                    }, 400); // 1秒后收缩
                }
            }

            // 处理链接点击 - 根据设置决定是否使用iframe
            handleLinkClick(url, serviceName, albumData) {
                console.log('处理链接点击:', url, serviceName, this.iframeModeEnabled);
                
                // 复制到剪贴板
                this.copyToClipboard(url);
                
                // 显示提示
                this.showStatusMessage(this.formatText('linkCopied', {
                    service: serviceName
                }));
                
                // 检查是否应该使用iframe模式
                let shouldUseIframe = false;
                
                if (url.includes('musicbrainz.org')) {
                    shouldUseIframe = this.iframeMusicBrainzModeEnabled;
                } else if (url.includes('listenbrainz.org')) {
                    shouldUseIframe = this.iframeListenBrainzModeEnabled;
                } else {
                    // 其他音乐流媒体服务
                    const isMusicService = this.isMusicStreamingService(url);
                    shouldUseIframe = this.iframeModeEnabled && isMusicService;
                }
                
                if (shouldUseIframe) {
                    // 使用iframe模式打开
                    this.openIframePlayer(url, albumData);
                } else {
                    // 在新标签页打开
                    window.open(url, '_blank');
                }
            }

            // 判断是否为音乐流媒体服务
            isMusicStreamingService(url) {
                const musicServices = [
                    'spotify.com',
                    'music.youtube.com',
                    'music.apple.com',
                    'itunes.apple.com',
                    'deezer.com',
                    'tidal.com',
                    'music.163.com',
                    'soundcloud.com',
                    'bandcamp.com',
                    'music.amazon.com'
                ];
                
                return musicServices.some(service => url.includes(service));
            }

            // 转换URL为iframe兼容的embed格式
            convertToEmbedUrl(url) {
                try {
                    console.log('开始转换URL:', url);
                    
                    // Spotify链接转换
                    if (url.includes('open.spotify.com')) {
                        console.log('检测到Spotify链接');
                        
                        // 移除查询参数
                        const cleanUrl = url.split('?')[0];
                        console.log('清理后的URL:', cleanUrl);
                        
                        // 检查是否已经是embed格式
                        if (cleanUrl.includes('/embed/')) {
                            console.log('已经是embed格式');
                            return url;
                        }
                        
                        // 转换为embed格式
                        const embedUrl = cleanUrl.replace('open.spotify.com/', 'open.spotify.com/embed/');
                        console.log('转换为embed格式:', embedUrl);
                        
                        // 添加必要的查询参数
                        const finalUrl = embedUrl + '?utm_source=generator';
                        console.log('最终URL:', finalUrl);
                        return finalUrl;
                    }
                    
                    // YouTube Music链接转换
                    if (url.includes('music.youtube.com')) {
                        console.log('检测到YouTube Music链接，暂时返回原URL');
                        return url;
                    }
                    
                    // 网易云音乐163链接转换
                    if (url.includes('music.163.com')) {
                        console.log('检测到网易云音乐163链接');
                        
                        // 从URL中提取ID
                        // 例如：https://music.163.com/outchain/player?type=1&id=73257118&auto=1&height=430
                        const idMatch = url.match(/[?&]id=(\d+)/);
                        
                        if (idMatch && idMatch[1]) {
                            const musicId = idMatch[1];
                            const convertedUrl = `https://notion.busiyi.world/music-player/?server=netease&type=album&id=${musicId}&list-max-height=96`;
                            console.log('163链接转换成功:', convertedUrl);
                            return convertedUrl;
                        } else {
                            console.warn('无法从163链接中提取ID，返回原URL');
                            return url;
                        }
                    }
                    
                    // 其他服务暂时返回原URL
                    console.log('其他服务，返回原URL');
                    return url;
                    
                } catch (error) {
                    console.error('URL转换失败:', error);
                    return url;
                }
            }
        }

        // 启动屏保
        window.addEventListener('DOMContentLoaded', () => {
            window.screensaver = new ThreeJSScreensaver();
            
            // 暴露测试方法到全局，方便调试
            window.test163 = () => window.screensaver.test163Link();
            
            console.log('网易云音乐163支持已添加！');
            console.log('测试方法：在控制台输入 test163() 来测试163链接功能');
        });
    </script>
</body>
</html>
